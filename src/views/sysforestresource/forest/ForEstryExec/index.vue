<template>
    <div class="app-container">
        <div ref="searchDom">
            <el-collapse-transition>
                <el-form :model="queryParams" ref="queryForm" class='queryClass form-line' :inline="true"
                    v-show="showSearch" label-width="68px">
                    <el-row :gutter="20">
                        <el-col :span="6">
                            <el-form-item label="单位" prop="orgCode">
                                <el-cascader ref="farmSelectTop" placeholder="请选择单位" v-model="queryParams.orgCode"
                                    @clear="queryParams.orgCode = null;queryParams.orgName = null"
                                    :options="orgTreeList" :props="{
                                    value: 'orgNo',
                                    label: 'orgName',
                                    expandTrigger: 'hover',
                                    checkStrictly: true,
                                    emitPath:false,
                                }" size="medium" clearable @change="farmHandleChangeTop">
                                </el-cascader>
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label="年度" prop="yearNo">
                                <el-date-picker v-model="queryParams.yearNo" value-format="YYYY" type="year" clearable
                                    placeholder="请选择年度">
                                </el-date-picker>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item>
                                <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
                                <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
            </el-collapse-transition>
        </div>

        <el-row :gutter="10" class="mb8 plotClass">
            <el-col :span="1.5" v-hasPermi="['ForEstryExec:insert']">
                <el-button type="primary" plain icon="Plus" @click="handleAdd"
                    v-hasPermi="['ForEstryExec:insert']">新增</el-button>
            </el-col>
            <el-col :span="1.5" v-hasPermi="['ForEstryExec:update']">
                <el-button v-if="showEdit" type="success" plain icon="Edit" :disabled="single" @click="handleUpdate"
                    v-hasPermi="['ForEstryExec:update']">修改</el-button>
            </el-col>
            <el-col :span="1.5" v-hasPermi="['ForEstryExec:logicDeleteByIds']">
                <el-button v-if="showEdit" type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete"
                    v-hasPermi="['ForEstryExec:logicDeleteByIds']">删除</el-button>
            </el-col>
            <el-col :span="1.5">
                <exceldownload ref="exceldownload" :param="queryParams" :url="url" :exceltext="exceltext">
                </exceldownload>
            </el-col>
            <el-col v-if="queryParams.stepOperation != 1" :span="1.5">
                <el-button type="info" plain @click="stepUp">返回上级</el-button>
            </el-col>
            <el-col :span="1.5"></el-col>
            <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>

        <el-table @row-click="rowClick" :height="tableHeight" :data="ForEstryExecList"
            @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" align="center" />
            <!-- <el-table-column label="完成id" align="center" prop="execId" />
      <el-table-column label="组织机构编码" align="center" prop="orgCode" /> -->
            <el-table-column label="单位" align="center" prop="orgName" />
            <el-table-column label="年度" align="center" prop="yearNo" />
            <el-table-column label="总产值" align="center" prop="totalValue" />
            <el-table-column label="经济林" align="center">
                <el-table-column label="面积" align="center" prop="forestsArea" />
                <el-table-column label="产值" align="center" prop="forestsValue" />
            </el-table-column>
            <el-table-column label="中草药" align="center">
                <el-table-column label="面积" align="center" prop="herbalArea" />
                <el-table-column label="产值" align="center" prop="herbalValue" />
            </el-table-column>
            <el-table-column label="食用菌" align="center">
                <el-table-column label="万袋" align="center" prop="fungiNum" />
                <el-table-column label="产值" align="center" prop="fungiValue" />
            </el-table-column>
            <el-table-column label="特种养殖" align="center">
                <el-table-column label="万只" align="center" prop="breedNum" />
                <el-table-column label="产值" align="center" prop="breedValue" />
            </el-table-column>
            <el-table-column label="其它林下产业" align="center">
                <el-table-column label="产值" align="center" prop="otherValue" />
            </el-table-column>
            <el-table-column label="统计时间" align="center" prop="opTime" width="180">
                <template #default="scope">
                    <span>{{ parseTime(scope.row.opTime, '{y}-{m}-{d}') }}</span>
                </template>
            </el-table-column>
            <el-table-column label="项目是否落实" align="center" prop="workFlag" />
            <!-- <el-table-column label="备注" align="center" prop="remark" />
      <el-table-column label="数据状态" align="center" prop="dataStatus" />
      <el-table-column label="生效状态 1：生效，2：失效" align="center" prop="statusCd" /> -->
            <el-table-column label="操作" align="center" fixed="right" width="120" class-name="small-padding fixed-width">
                <template #default="scope">
                    <el-button v-if="!showEdit && scope.row.orgName != '合计'" link type="primary" icon="Edit"
                        @click.stop="stepDown(scope.row)">查看明细</el-button>
                    <el-button v-if="showEdit && scope.row.orgName != '合计'" link type="primary" icon="Edit"
                        @click.stop="handleUpdate(scope.row)" v-hasPermi="['ForEstryExec:update']">修改</el-button>
                    <el-button v-if="showEdit && scope.row.orgName != '合计'" link type="primary" icon="Delete"
                        @click.stop="handleDelete(scope.row)"
                        v-hasPermi="['ForEstryExec:logicDeleteById']">删除</el-button>
                </template>
            </el-table-column>
        </el-table>

        <pagination v-show="total>0" :total="total" v-model:page="queryParams.page" v-model:limit="queryParams.rows"
            @pagination="getList" />

        <!-- 添加或修改林业产业完成情况对话框 -->
        <el-dialog :title="title" v-model="open" width="800px" append-to-body :before-close="cancel"
            :close-on-click-modal="false">
            <el-form ref="formRef" :model="form" :rules="rules" label-width="130px">
                <el-row>
                    <el-form-item label="组织机构编码" prop="orgCode">
                        <el-cascader ref="farmSelect" style="width: 100%" placeholder="请选择单位" v-model="form.orgCode"
                            @clear="form.orgCode = null;form.orgName = null" :options="orgTreeList" :props="{
                                value: 'orgNo',
                                label: 'orgName',
                                expandTrigger: 'hover',
                                checkStrictly: true,
                                emitPath:false,
                            }" size="medium" clearable @change="farmHandleChange">
                        </el-cascader>
                    </el-form-item>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="年度" prop="yearNo">
                            <el-date-picker style="width: 100%" v-model="form.yearNo" value-format="YYYY" type="year"
                                clearable placeholder="请选择年度">
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="统计时间" prop="opTime">
                            <el-date-picker style="width: 100%" clearable size="small" v-model="form.opTime" type="date"
                                value-format="YYYY-MM-DD HH:mm:ss" placeholder="选择统计时间">
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="经济林-面积" prop="forestsArea">
                            <el-input v-model="form.forestsArea" placeholder="请输入经济林-面积">
                                <span slot="suffix" style="color:#000">亩</span>
                            </el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="经济林-产值" prop="forestsValue">
                            <el-input v-model="form.forestsValue" placeholder="请输入经济林-产值">
                                <span slot="suffix" style="color:#000">万元</span>
                            </el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="中草药-面积" prop="herbalArea">
                            <el-input v-model="form.herbalArea" placeholder="请输入中草药-面积">
                                <span slot="suffix" style="color:#000">亩</span>
                            </el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="中草药-产值" prop="herbalValue">
                            <el-input v-model="form.herbalValue" placeholder="请输入中草药-产值">
                                <span slot="suffix" style="color:#000">万元</span>
                            </el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="食用菌-万袋" prop="fungiNum">
                            <el-input v-model="form.fungiNum" placeholder="请输入食用菌-万袋" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="食用菌-产值" prop="fungiValue">
                            <el-input v-model="form.fungiValue" placeholder="请输入食用菌-产值">
                                <span slot="suffix" style="color:#000">万元</span>
                            </el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="特种养殖-万只" prop="breedNum">
                            <el-input v-model="form.breedNum" placeholder="请输入特种养殖-万只" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="特种养殖-产值" prop="breedValue">
                            <el-input v-model="form.breedValue" placeholder="请输入特种养殖-产值">
                                <span slot="suffix" style="color:#000">万元</span>
                            </el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="其它林下产业-产值" prop="otherValue">
                            <el-input v-model="form.otherValue" placeholder="请输入其它林下产业-产值">
                                <span slot="suffix" style="color:#000">万元</span>
                            </el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="总产值" prop="totalValue">
                            <el-input v-model="form.totalValue" placeholder="请输入总产值">
                                <span slot="suffix" style="color:#000">万元</span>
                            </el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-form-item label="项目是否落实" prop="workFlag">
                    <el-input v-model="form.workFlag" maxlength="40" placeholder="请输入项目是否落实" />
                </el-form-item>
                <el-form-item label="备注" prop="remark">
                    <el-input v-model="form.remark" maxlength="200" type="textarea" :rows="2" placeholder="请输入备注" />
                </el-form-item>
                <!-- <el-form-item label="数据状态" prop="dataStatus">
          <el-input v-model="form.dataStatus" placeholder="请输入数据状态" />
        </el-form-item> -->
                <!-- <el-form-item label="生效状态 1：生效，2：失效" prop="statusCd">
          <el-input v-model="form.statusCd" placeholder="请输入生效状态 1：生效，2：失效" />
        </el-form-item> -->
                <!-- <el-form-item label="组织机构名称" prop="orgName">
          <el-input v-model="form.orgName" placeholder="请输入组织机构名称" />
        </el-form-item> -->
            </el-form>
            <template #footer>
                <div slot="footer" class="dialog-footer">
                    <el-button type="primary" @click="submitForm">确 定</el-button>
                    <el-button @click="cancel">取 消</el-button>
                </div>
            </template>
        </el-dialog>
        <!-- 详情 -->
        <el-dialog title="林业产业完成情况详情" class="detailDialog" v-model="openDetail" width="800px" append-to-body
            :before-close="cancel" :close-on-click-modal="false">
            <el-form ref="formRef" :model="form" label-width="180px">
                <el-form-item label="组织机构编码" prop="orgCode">
                    <el-cascader ref="farmSelect" disabled placeholder="" v-model="form.orgCode"
                        @clear="form.orgCode = null;form.orgName = null" :options="orgTreeList" :props="{
                            value: 'orgNo',
                            label: 'orgName',
                            expandTrigger: 'hover',
                            checkStrictly: true,
                            emitPath:false,
                        }" size="medium" clearable @change="farmHandleChange">
                    </el-cascader>
                </el-form-item>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="年度" prop="yearNo">
                            <span style="padding-left: 15px;">{{form.yearNo}}</span>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="统计时间" prop="opTime">
                            <span style="padding-left: 15px;"
                                v-if="form.opTime!=null">{{form.opTime.split("")[0]}}</span>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="经济林-面积" prop="forestsArea">
                            <span style="padding-left: 15px;">{{form.forestsArea}}</span>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="经济林-产值" prop="forestsValue">
                            <span style="padding-left: 15px;">{{form.forestsValue}}</span>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="中草药-面积" prop="herbalArea">
                            <span style="padding-left: 15px;">{{form.herbalArea}}</span>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="中草药-产值" prop="herbalValue">
                            <span style="padding-left: 15px;">{{form.herbalValue}}</span>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="食用菌-万袋" prop="fungiNum">
                            <span style="padding-left: 15px;">{{form.fungiNum}}</span>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="食用菌-产值" prop="fungiValue">
                            <span style="padding-left: 15px;">{{form.fungiValue}}</span>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="特种养殖-万只" prop="breedNum">
                            <span style="padding-left: 15px;">{{form.breedNum}}</span>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="特种养殖-产值" prop="breedValue">
                            <span style="padding-left: 15px;">{{form.breedValue}}</span>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="其它林下产业-产值" prop="otherValue">
                            <span style="padding-left: 15px;">{{form.otherValue}}</span>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="总产值" prop="totalValue">
                            <span style="padding-left: 15px;">{{form.totalValue}}</span>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-form-item label="项目是否落实" prop="workFlag">
                    <span style="padding-left: 15px;">{{form.workFlag}}</span>
                </el-form-item>
                <el-form-item label="备注" prop="remark">
                    <span style="padding-left: 15px;">{{form.remark}}</span>
                </el-form-item>
            </el-form>
        </el-dialog>
    </div>
</template>

<script>
    import "@/views/sysforestresource/assets/styles/index.scss";
    import { listForEstryExec, getForEstryExec, delForEstryExec, addForEstryExec, updateForEstryExec, delForEstryExecs, queryOrgCode } from "@/api/sysforestresource/forest/ForEstryExec";
    import { queryOrgTreeByUserOrg } from "@/api/sysforestresource/login";
    import exceldownload from "@/views/sysforestresource/components/exceldownload";
    export default {
        name: "/forest/ForEstryExec/queryByPage",
        components: {
            exceldownload,
        },
        data() {
            var validateNum = (rule, value, callback) => {
                var reg = /^(([1-9]{1}\d*)|(0{1}))(\.\d{1,2})?$/
                if (value === null || value === '' || typeof (value) == 'undefined') {
                    callback()
                } else if (value.toString().split('.')[0].length > 10) {
                    callback(new Error('数字过大，请确认'))
                } else if (reg.test(value)) {
                    callback()
                } else {
                    callback(new Error('请输入数字(可保留两位小数)'))
                }
            }
            return {
                // 遮罩层
                loading: true,
                // 选中数组
                ids: [],
                names: [],
                // 非单个禁用
                single: true,
                // 非多个禁用
                multiple: true,
                // 显示搜索条件
                showSearch: true,
                // 总条数
                total: 0,
                // 林业产业完成情况表格数据
                ForEstryExecList: [],
                // 弹出层标题
                title: "",
                // 是否显示弹出层
                open: false,
                openDetail: false,
                // 查询参数
                queryParams: {
                    page: 1,
                    rows: 10,
                    orgCode: null,
                    orgName: null,
                    yearNo: null,
                    totalValue: null,
                    forestsArea: null,
                    forestsValue: null,
                    herbalArea: null,
                    herbalValue: null,
                    fungiNum: null,
                    fungiValue: null,
                    breedNum: null,
                    breedValue: null,
                    otherValue: null,
                    opTime: null,
                    workFlag: null,
                    remark: null,
                    dataStatus: null,
                    statusCd: null,
                    stepOperation: 1,
                },

                stepParams: {
                    stepOne: {
                        orgCode: null,
                        yearNo: null,
                    },
                    stepTwe: {
                        orgCode: null,
                        yearNo: null,
                    },
                    stepThree: {
                        orgCode: null,
                        yearNo: null,
                    },
                },

                orgCode: '',
                orgLevel: 0,
                showEdit: false,

                exceltext: "导出",
                url: import.meta.env.VITE_APP_BASE_API + "/bdh-forest-resource-api/forest/ForEstryExec/export",

                // 表单参数
                form: {},
                // 表单校验
                rules: {
                    orgCode: [{
                        required: true,
                        message: '请选择单位',
                        trigger: 'change'
                    }],
                    yearNo: [{
                        required: true,
                        message: '请选择年度',
                        trigger: 'change'
                    }],
                    opTime: [{
                        required: true,
                        message: '请选择统计时间',
                        trigger: 'change'
                    }],
                    totalValue: [{
                        required: false,
                        validator: validateNum,
                        trigger: 'blur'
                    }],
                    forestsArea: [{
                        required: false,
                        validator: validateNum,
                        trigger: 'blur'
                    }],
                    forestsValue: [{
                        required: false,
                        validator: validateNum,
                        trigger: 'blur'
                    }],
                    herbalArea: [{
                        required: false,
                        validator: validateNum,
                        trigger: 'blur'
                    }],
                    herbalValue: [{
                        required: false,
                        validator: validateNum,
                        trigger: 'blur'
                    }],
                    fungiNum: [{
                        required: false,
                        validator: validateNum,
                        trigger: 'blur'
                    }],
                    fungiValue: [{
                        required: false,
                        validator: validateNum,
                        trigger: 'blur'
                    }],
                    breedNum: [{
                        required: false,
                        validator: validateNum,
                        trigger: 'blur'
                    }],
                    breedValue: [{
                        required: false,
                        validator: validateNum,
                        trigger: 'blur'
                    }],
                    otherValue: [{
                        required: false,
                        validator: validateNum,
                        trigger: 'blur'
                    }],
                },
                orgTreeList: [],
                tableHeight: 200,
                searchHeight: 0,
            };
        },
        created() {
            this.getList();
            this.getOrgTreeByUserOrg();
        },
        mounted() {
            this.searchHeight = this.$refs.searchDom.clientHeight;
            this.tableHeight = this.showSearch
                ? window.innerHeight - this.searchHeight - 220
                : window.innerHeight - 220
        },
        watch: {
            //搜索隐藏调整表格大小
            showSearch() {
                this.tableHeight = this.showSearch
                    ? window.innerHeight - this.searchHeight - 220
                    : window.innerHeight - 220;
            },
        },
        methods: {

            initEditButton() {
                this.showEdit = false;
                if (this.orgLevel >= 3) {
                    this.showEdit = true;
                    return;
                }
                if (this.orgLevel == 2) {
                    if (this.queryParams.stepOperation == 2) {
                        this.showEdit = true;
                        return;
                    }
                }
                if (this.orgLevel == 1) {
                    if (this.queryParams.stepOperation == 3) {
                        this.showEdit = true;
                        return;
                    }
                }
            },
            stepDown(row) {
                if (this.orgLevel >= 3) return;
                if (this.orgLevel == 2) {
                    if (this.queryParams.stepOperation == 2) {
                        return;
                    }
                }
                if (this.orgLevel == 1) {
                    if (this.queryParams.stepOperation == 3) {

                        return;
                    }
                }
                this.queryParams.stepOperation += 1;
                this.queryParams.orgCode = row.orgCode;
                this.queryParams.yearNo = String(row.yearNo);
                this.getList();
                if (this.queryParams.stepOperation == 1) {
                    this.stepParams.stepOne.orgCode = this.queryParams.orgCode
                    this.stepParams.stepOne.yearNo = this.queryParams.yearNo
                } else if (this.queryParams.stepOperation == 2) {
                    this.stepParams.stepTwe.orgCode = this.queryParams.orgCode
                    this.stepParams.stepTwe.yearNo = this.queryParams.yearNo
                } else if (this.queryParams.stepOperation == 3) {
                    this.stepParams.stepThree.orgCode = this.queryParams.orgCode
                    this.stepParams.stepThree.yearNo = this.queryParams.yearNo
                }
                this.initEditButton();
            },
            stepUp() {
                if (this.queryParams.stepOperation == 1) return;
                this.queryParams.stepOperation -= 1;
                if (this.queryParams.stepOperation == 1) {
                    this.queryParams.orgCode = this.stepParams.stepOne.orgCode
                    this.queryParams.yearNo = this.stepParams.stepOne.yearNo
                } else if (this.queryParams.stepOperation == 2) {
                    this.queryParams.orgCode = this.stepParams.stepTwe.orgCode
                    this.queryParams.yearNo = this.stepParams.stepTwe.yearNo
                } else if (this.queryParams.stepOperation == 3) {
                    this.queryParams.orgCode = this.stepParams.stepThree.orgCode
                    this.queryParams.yearNo = this.stepParams.stepThree.yearNo
                } else if (this.queryParams.stepOperation == 4) {
                    this.queryParams.orgCode = this.stepParams.stepFour.orgCode
                    this.queryParams.yearNo = this.stepParams.stepFour.yearNo
                }
                this.getList();
                this.initEditButton();
            },


            /** 查询林业产业完成情况列表 */
            getList() {
                this.loading = true;
                listForEstryExec(this.queryParams).then(response => {
                    this.ForEstryExecList = response.data.records;
                    this.total = response.data.total;
                    this.loading = false;
                });
            },
            // 取消按钮
            cancel() {
                this.open = false;
                this.openDetail = false;
                this.reset();
            },
            // 表单重置
            reset() {
                this.form = {
                    execId: null,
                    orgCode: null,
                    orgName: null,
                    yearNo: null,
                    totalValue: null,
                    forestsArea: null,
                    forestsValue: null,
                    herbalArea: null,
                    herbalValue: null,
                    fungiNum: null,
                    fungiValue: null,
                    breedNum: null,
                    breedValue: null,
                    otherValue: null,
                    opTime: null,
                    workFlag: null,
                    remark: null,
                    dataStatus: null,
                    createBy: null,
                    createTime: null,
                    updateBy: null,
                    updateTime: null,
                    statusCd: null
                };
                this.resetForm("formRef");
            },
            /** 搜索按钮操作 */
            handleQuery() {
                this.queryParams.page = 1;
                this.getList();
            },
            /** 重置按钮操作 */
            resetQuery() {
                this.resetForm("queryForm");
                this.handleQuery();
            },
            // 多选框选中数据
            handleSelectionChange(selection) {
                // this.ids = selection.map(item => item.execId)
                this.ids = selection.filter(item => item.execId)
                this.ids = this.ids.map(item => item.execId)

                this.names = selection.map(item => item.orgName)
                this.single = this.ids.length !== 1
                // this.multiple = !selection.length
                this.multiple = !this.ids.length
            },
            /** 新增按钮操作 */
            handleAdd() {
                this.reset();
                this.open = true;
                this.title = "添加林业产业完成情况";
            },
            /** 修改按钮操作 */
            handleUpdate(row) {
                this.reset();
                const execId = row.execId || this.ids
                getForEstryExec(execId).then(response => {
                    this.form = response.data;
                    this.form.yearNo = this.form.yearNo.toString()
                    this.form.geom = null;
                    this.open = true;
                    this.title = "修改林业产业完成情况";
                });
            },
            rowClick(row) {
                this.reset();
                const execId = row.execId || this.ids
                getForEstryExec(execId).then(response => {
                    this.form = response.data;
                    this.openDetail = true;
                });
            },
            /** 提交按钮 */
            submitForm() {
                delete this.form.geom
                this.$refs["formRef"].validate(valid => {
                    if (valid) {
                        if (this.form.execId != null) {
                            updateForEstryExec(this.form).then(response => {
                                this.$message({
                                    message: "修改成功",
                                    type: 'success'
                                });
                                this.open = false;
                                this.getList();
                            });
                        } else {
                            addForEstryExec(this.form).then(response => {
                                this.$message({
                                    message: "新增成功",
                                    type: 'success'
                                })
                                this.open = false;
                                this.getList();
                            });
                        }
                    }
                });
            },
            /** 删除按钮操作 */
            handleDelete(row) {
                const execIds = row.execId || this.ids;
                const execNames = row.orgName || this.names;
                this.$confirm('是否确认删除林业产业完成情况单位为"' + execNames + '"的数据项?', "警告", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning"
                }).then(function () {
                    return Array.isArray(execIds) ? delForEstryExecs(execIds) : delForEstryExec(execIds);
                }).then(() => {
                    this.getList();
                    this.$message({
                        message: "删除成功",
                        type: 'success'
                    });
                })
            },
            farmHandleChangeTop(value) {
                if (this.$refs.farmSelectTop) {
                    this.$refs.farmSelectTop.togglePopperVisible() //监听值发生变化就关闭它
                }
                var node = this.$refs.farmSelectTop.getCheckedNodes()[0]
                if (node != null) {
                    let data = node.data
                    this.form.orgName = data.orgFullName
                } else {
                    this.form.orgName = null
                }
            },
            farmHandleChange(value) {
                if (this.$refs.farmSelect) {
                    this.$refs.farmSelect.togglePopperVisible() //监听值发生变化就关闭它
                }
                var node = this.$refs.farmSelect.getCheckedNodes()[0]
                if (node != null) {
                    let data = node.data
                    this.form.orgName = data.orgFullName
                } else {
                    this.form.orgName = null
                }
            },
            async getOrgTreeByUserOrg() {
                let res = await queryOrgTreeByUserOrg();
                let {
                    data,
                    code
                } = res;
                this.orgTreeList = data.children;

                let resOrg = await queryOrgCode();
                this.orgCode = String(resOrg.data);

                if (this.orgCode.length == 2) {
                    this.orgLevel = 1
                } else if (this.orgCode.length == 4) {
                    this.orgLevel = 2
                } else if (this.orgCode.length == 6) {
                    this.orgLevel = 3
                } else {
                    this.orgLevel = 4
                }

                this.initEditButton();

            },
        }
    };


</script>
<style scoped>
    .detailDialog :deep(.el-input__inner) {
        border: none;
        color: #606266;
        background-color: transparent;
        cursor: default;
    }

    .detailDialog :deep(.el-input.is-disabled .el-input__suffix) {
        display: none;
    }
</style>
