<template>
    <div class='app-container'>
        <div ref="searchDom">
            <el-collapse-transition>
                <el-form :model='queryParams' ref='queryForm' class='queryClass form-line' :inline='true'
                    v-show='showSearch' label-width='68px'>
                    <el-row :gutter="20">
                        <el-col :span="6">
                            <el-form-item label='单位' prop='orgCode'>
                                <el-cascader ref='farmSelectTop' style='width: 400px' placeholder='请选择单位'
                                    v-model='queryParams.orgCode'
                                    @clear='queryParams.orgCode = null; queryParams.orgCode = null'
                                    :options='orgTreeList' :props="{
                                    value: 'orgNo',
                                    label: 'orgName',
                                    expandTrigger: 'hover',
                                    checkStrictly: true,
                                    emitPath: false,
                                }" size='medium' clearable @change='farmHandleChangeTop'>
                                </el-cascader>
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label='年度' prop='yearNo'>
                                <el-date-picker v-model='queryParams.yearNo' value-format='YYYY' type='year' clearable
                                    placeholder='请选择年度'>
                                </el-date-picker>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item>
                                <el-button type='primary' icon='Search' @click='handleQuery'>搜索</el-button>
                                <el-button icon='Refresh' @click='resetQuery'>重置</el-button>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <!--      <el-form-item label='状态' prop='forStaApplyStatus'>-->
                    <!--        <el-select v-model='queryParams.forStaApplyStatus' placeholder='请选择状态' clearable-->
                    <!--                   @clear='queryParams.forStaApplyStatus = null'>-->
                    <!--          <el-option v-for='dict in forStaApplyStatusOptions' :key='dict.code' :label='dict.name' :value='dict.code' />-->
                    <!--        </el-select>-->
                    <!--      </el-form-item>-->
                </el-form>
            </el-collapse-transition>
        </div>

        <el-row :gutter='10' class='mb8 plotClass'>
            <!-- <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"

        >新增</el-button>
      </el-col> -->
            <!-- <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"

        >修改</el-button>
      </el-col> -->
            <!-- <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"

        >删除</el-button>
      </el-col> -->
            <el-col :span='1.5' v-hasPermi="['ForAnnualForestResStatistics:exportTemplate']">
                <exceldownload ref='exceldownload' :param='queryParams' :url='url'
                    v-hasPermi="['ForAnnualForestResStatistics:exportTemplate']"></exceldownload>
            </el-col>
            <el-col :span='1.5' v-hasPermi="['ForAnnualForestResStatistics:importExcel']">
                <FileUpload1 :isShowTip='false' :default="['xlsx']" :text1="'导入'" :uploadFileUrl='uploadFileUrl'
                    @newlist='getList' v-hasPermi="['ForAnnualForestResStatistics:importExcel']"></FileUpload1>
            </el-col>
            <right-toolbar v-model:showSearch="showSearch" @queryTable='getList'></right-toolbar>
        </el-row>

        <el-table :height="tableHeight" :data='ForAnnualForestResStatisticsList'
            @selection-change='handleSelectionChange' :span-method='objectSpanMethod'>
            <el-table-column type='selection' width='55' align='center' fixed="left" />
            <el-table-column label='单位' align='center' prop='orgName' fixed="left" />
            <el-table-column label='年度' align='center' prop='yearNo' fixed="left" />
            <el-table-column label='林木所有权' align='center' prop='forestOwnershipCategory'
                :formatter='forestOwnershipCategoryFormat' />
            <el-table-column label='上年实有' align='center'>
                <el-table-column label='面积(公顷)' align='center' prop='pyacAreaHectares' />
                <el-table-column label='蓄积(立方米)' align='center' prop='pyacVolumeCubicMeters' />
            </el-table-column>
            <el-table-column label='新增有林地面积' align='center'>
                <el-table-column label='合计(公顷)' align='center' prop='nfaTotalHectares' />
                <el-table-column label='更新造林' align='center'>
                    <el-table-column label='人工造林(公顷)' align='center' prop='nfapfForestryHectares' />
                    <el-table-column label='迹地更新(公顷)' align='center' prop='nfaTraceUpdatedHectares' />
                </el-table-column>
                <el-table-column label='封山育林(公顷)' align='center' prop='nfacmForestryHectares' />
            </el-table-column>
            <el-table-column label='新增有林地蓄积(立方米)' align='center' prop='nfVolumeCubicMeters' />
            <el-table-column label='活力木总生长量(立方米)' align='center' prop='tgVolumeCubicMeters' />
            <el-table-column label='本年度减少' align='center'>
                <el-table-column label='合计' align='center'>
                    <el-table-column label='面积(公顷)' align='center' prop='cyrtAreaHectares' />
                    <el-table-column label='蓄积(立方米)' align='center' prop='cyrtVolumeCubicMeters' />
                </el-table-column>
                <el-table-column label='林业经营活动' align='center'>
                    <el-table-column label='小计' align='center'>
                        <el-table-column label='面积(公顷)' align='center' prop='cyrfosAreaHectares' />
                        <el-table-column label='蓄积(立方米)' align='center' prop='cyrfosVolumeCubicMeters' />
                    </el-table-column>
                    <el-table-column label='采伐' align='center'>
                        <el-table-column label='面积(公顷)' align='center' prop='cyrfolAreaHectares' />
                        <el-table-column label='蓄积(立方米)' align='center' prop='cyrfolVolumeCubicMeters' />
                    </el-table-column>
                    <el-table-column label='征占用地' align='center'>
                        <el-table-column label='面积(公顷)' align='center' prop='cyrfolvaAreaHectares' />
                        <el-table-column label='蓄积(立方米)' align='center' prop='cyrfolvaVolumeCubicMeters' />
                    </el-table-column>
                </el-table-column>
                <el-table-column label='非林业经营活动' align='center'>
                    <el-table-column label='小计' align='center'>
                        <el-table-column label='面积(公顷)' align='center' prop='cyrnfopsAreaHectares' />
                        <el-table-column label='蓄积(立方米)' align='center' prop='cyrnfosVolumeCubicMeters' />
                    </el-table-column>
                    <el-table-column label='乱砍滥伐' align='center'>
                        <el-table-column label='面积(公顷)' align='center' prop='cyrnfodlAreaHectares' />
                        <el-table-column label='蓄积(立方米)' align='center' prop='cyrnfodlVolumeCubicMeters' />
                    </el-table-column>
                    <el-table-column label='毁林开荒' align='center'>
                        <el-table-column label='面积(公顷)' align='center' prop='cyrnfodAreaHectares' />
                        <el-table-column label='蓄积(立方米)' align='center' prop='cyrnfodVolumeCubicMeters' />
                    </el-table-column>
                    <el-table-column label='火灾' align='center'>
                        <el-table-column label='面积(公顷)' align='center' prop='cyrnfofAreaHectares' />
                        <el-table-column label='蓄积(立方米)' align='center' prop='cyrnfofVolumeCubicMeters' />
                    </el-table-column>
                    <el-table-column label='有害生物' align='center'>
                        <el-table-column label='面积(公顷)' align='center' prop='cyrnfopAreaHectares' />
                        <el-table-column label='蓄积(立方米)' align='center' prop='cyrnfopVolumeCubicMeters' />
                    </el-table-column>
                    <el-table-column label='其他' align='center'>
                        <el-table-column label='面积(公顷)' align='center' prop='cyrnfooAreaHectares' />
                        <el-table-column label='蓄积(立方米)' align='center' prop='cyrnfooVolumeCubicMeters' />
                    </el-table-column>
                </el-table-column>
                <el-table-column label='自然枯损' align='center'>
                    <el-table-column label='蓄积(立方米)' align='center' prop='cynlVolumeCubicMeters' />
                </el-table-column>
            </el-table-column>
            <el-table-column label='本年度净增值(+-)' align='center'>
                <el-table-column label='面积(公顷)' align='center' prop='cyniAreaHectares' />
                <el-table-column label='蓄积(立方米)' align='center' prop='cyniVolumeCubicMeters' />
            </el-table-column>
            <el-table-column label='修正值(+-)' align='center'>
                <el-table-column label='面积(公顷)' align='center' prop='correctionsAreaHectares' />
                <el-table-column label='蓄积(立方米)' align='center' prop='cvolumeCubicMeters' />
            </el-table-column>
            <el-table-column label='本年度实有' align='center'>
                <el-table-column label='面积(公顷)' align='center' prop='cyacAreaHectares' />
                <el-table-column label='蓄积(立方米)' align='center' prop='cyacVolumeCubicMeters' />
            </el-table-column>
            <el-table-column label='备注' align='center' prop='remark' />
            <el-table-column label='状态' align='center' prop='forStaApplyStatus' fixed="right"
                :formatter='forStaApplyStatusFormat' />
            <el-table-column label='操作' align='center' class-name='small-padding fixed-width' style='width: 150px'
                fixed="right">
                <template #default="scope">
                    <!--<el-button

            type='text'
            icon='Edit'
            @click='handleUpdate(scope.row)'
          >修改
          </el-button>
          <el-button

            type='text'
            icon='Delete'
            @click='handleDelete(scope.row)'
          >删除
          </el-button>-->
                    <template v-if='scope.row.forStaApplyStatus == "3"'>
                        <el-button type='text' v-hasPermi="['ForAnnualForestResStatistics:submit']"
                            @click='handleDataSubmit(scope.row)'>提交
                        </el-button>
                    </template>
                    <template v-if='scope.row.forStaApplyStatus == "2"'>
                        <el-button type='text' v-hasPermi="['ForAnnualForestResStatistics:back']"
                            @click='handleDataBack(scope.row)'>退回
                        </el-button>
                    </template>
                </template>
            </el-table-column>
        </el-table>

        <pagination v-show='total > 0' :total='total' v-model:page='queryParams.page' v-model:limit='queryParams.rows'
            @pagination='getList' />

        <!-- 添加或修改年度森林资源统计表对话框 -->
        <el-dialog :title='title' v-model='open' width='500px' append-to-body>
            <el-form ref='formRef' :model='form' :rules='rules' label-width='80px'>
                <el-form-item label='主键id' prop='forStatisticsId'>
                    <el-input v-model='form.forStatisticsId' placeholder='请输入主键id' />
                </el-form-item>
                <el-form-item label='组织机构编码' prop='orgCode'>
                    <el-input v-model='form.orgCode' placeholder='请输入组织机构编码' />
                </el-form-item>
                <el-form-item label='组织机构名称' prop='orgName'>
                    <el-input v-model='form.orgName' placeholder='请输入组织机构名称' />
                </el-form-item>
                <el-form-item label='年度' prop='yearNo'>
                    <el-input v-model='form.yearNo' placeholder='请输入年度' />
                </el-form-item>
                <el-form-item label='林木所有权分类|字典值forest_ownership_category' prop='forestOwnershipCategory'>
                    <el-input v-model='form.forestOwnershipCategory'
                        placeholder='请输入林木所有权分类|字典值forest_ownership_category' />
                </el-form-item>
                <el-form-item label='上年实有面积(公顷)' prop='pyacAreaHectares'>
                    <el-input v-model='form.pyacAreaHectares' placeholder='请输入上年实有面积(公顷)' />
                </el-form-item>
                <el-form-item label='上年实有蓄积(立方米)' prop='pyacVolumeCubicMeters'>
                    <el-input v-model='form.pyacVolumeCubicMeters' placeholder='请输入上年实有蓄积(立方米)' />
                </el-form-item>
                <el-form-item label='新增有林地面积合计(公顷)' prop='nfaTotalHectares'>
                    <el-input v-model='form.nfaTotalHectares' placeholder='请输入新增有林地面积合计(公顷)' />
                </el-form-item>
                <el-form-item label='新增有林地面积_更新造林_人工造林(公顷)' prop='nfapfForestryHectares'>
                    <el-input v-model='form.nfapfForestryHectares' placeholder='请输入新增有林地面积_更新造林_人工造林(公顷)' />
                </el-form-item>
                <el-form-item label='新增有林地面积_更新造林_迹地更新(公顷)' prop='nfaTraceUpdatedHectares'>
                    <el-input v-model='form.nfaTraceUpdatedHectares' placeholder='请输入新增有林地面积_更新造林_迹地更新(公顷)' />
                </el-form-item>
                <el-form-item label='新增有林地面积_封山育林(公顷)' prop='nfacmForestryHectares'>
                    <el-input v-model='form.nfacmForestryHectares' placeholder='请输入新增有林地面积_封山育林(公顷)' />
                </el-form-item>
                <el-form-item label='新增有林地蓄积(立方米)' prop='nfVolumeCubicMeters'>
                    <el-input v-model='form.nfVolumeCubicMeters' placeholder='请输入新增有林地蓄积(立方米)' />
                </el-form-item>
                <el-form-item label='活力木总生长量(立方米)' prop='tgVolumeCubicMeters'>
                    <el-input v-model='form.tgVolumeCubicMeters' placeholder='请输入活力木总生长量(立方米)' />
                </el-form-item>
                <el-form-item label='本年度减少_合计_面积(公顷)' prop='cyrtAreaHectares'>
                    <el-input v-model='form.cyrtAreaHectares' placeholder='请输入本年度减少_合计_面积(公顷)' />
                </el-form-item>
                <el-form-item label='本年度减少_合计_蓄积(立方米)' prop='cyrtVolumeCubicMeters'>
                    <el-input v-model='form.cyrtVolumeCubicMeters' placeholder='请输入本年度减少_合计_蓄积(立方米)' />
                </el-form-item>
                <el-form-item label='本年度减少_林业经营活动_小计_面积(公顷)' prop='cyrfosAreaHectares'>
                    <el-input v-model='form.cyrfosAreaHectares' placeholder='请输入本年度减少_林业经营活动_小计_面积(公顷)' />
                </el-form-item>
                <el-form-item label='本年度减少_林业经营活动_小计_蓄积(立方米)' prop='cyrfosVolumeCubicMeters'>
                    <el-input v-model='form.cyrfosVolumeCubicMeters' placeholder='请输入本年度减少_林业经营活动_小计_蓄积(立方米)' />
                </el-form-item>
                <el-form-item label='本年度减少_林业经营活动_采伐_面积(公顷)' prop='cyrfolAreaHectares'>
                    <el-input v-model='form.cyrfolAreaHectares' placeholder='请输入本年度减少_林业经营活动_采伐_面积(公顷)' />
                </el-form-item>
                <el-form-item label='本年度减少_林业经营活动_采伐_蓄积(立方米)' prop='cyrfolVolumeCubicMeters'>
                    <el-input v-model='form.cyrfolVolumeCubicMeters' placeholder='请输入本年度减少_林业经营活动_采伐_蓄积(立方米)' />
                </el-form-item>
                <el-form-item label='本年度减少_林业经营活动_征占用地_面积(公顷)' prop='cyrfolvaAreaHectares'>
                    <el-input v-model='form.cyrfolvaAreaHectares' placeholder='请输入本年度减少_林业经营活动_征占用地_面积(公顷)' />
                </el-form-item>
                <el-form-item label='本年度减少_林业经营活动_征占用地_蓄积(立方米)' prop='cyrfolvaVolumeCubicMeters'>
                    <el-input v-model='form.cyrfolvaVolumeCubicMeters' placeholder='请输入本年度减少_林业经营活动_征占用地_蓄积(立方米)' />
                </el-form-item>
                <el-form-item label='本年度减少_非林业经营活动_小计_面积(公顷)' prop='cyrnfopsAreaHectares'>
                    <el-input v-model='form.cyrnfopsAreaHectares' placeholder='请输入本年度减少_非林业经营活动_小计_面积(公顷)' />
                </el-form-item>
                <el-form-item label='本年度减少_非林业经营活动_小计_蓄积(立方米)' prop='cyrnfosVolumeCubicMeters'>
                    <el-input v-model='form.cyrnfosVolumeCubicMeters' placeholder='请输入本年度减少_非林业经营活动_小计_蓄积(立方米)' />
                </el-form-item>
                <el-form-item label='本年度减少_非林业经营活动_乱砍滥伐_面积(公顷)' prop='cyrnfodlAreaHectares'>
                    <el-input v-model='form.cyrnfodlAreaHectares' placeholder='请输入本年度减少_非林业经营活动_乱砍滥伐_面积(公顷)' />
                </el-form-item>
                <el-form-item label='本年度减少_非林业经营活动_乱砍滥伐_蓄积(立方米)' prop='cyrnfodlVolumeCubicMeters'>
                    <el-input v-model='form.cyrnfodlVolumeCubicMeters' placeholder='请输入本年度减少_非林业经营活动_乱砍滥伐_蓄积(立方米)' />
                </el-form-item>
                <el-form-item label='本年度减少_非林业经营活动_毁林开荒_面积(公顷)' prop='cyrnfodAreaHectares'>
                    <el-input v-model='form.cyrnfodAreaHectares' placeholder='请输入本年度减少_非林业经营活动_毁林开荒_面积(公顷)' />
                </el-form-item>
                <el-form-item label='本年度减少_非林业经营活动_毁林开荒_蓄积(立方米)' prop='cyrnfodVolumeCubicMeters'>
                    <el-input v-model='form.cyrnfodVolumeCubicMeters' placeholder='请输入本年度减少_非林业经营活动_毁林开荒_蓄积(立方米)' />
                </el-form-item>
                <el-form-item label='本年度减少_非林业经营活动_火灾_面积(公顷)' prop='cyrnfofAreaHectares'>
                    <el-input v-model='form.cyrnfofAreaHectares' placeholder='请输入本年度减少_非林业经营活动_火灾_面积(公顷)' />
                </el-form-item>
                <el-form-item label='本年度减少_非林业经营活动_火灾_蓄积(立方米)' prop='cyrnfofVolumeCubicMeters'>
                    <el-input v-model='form.cyrnfofVolumeCubicMeters' placeholder='请输入本年度减少_非林业经营活动_火灾_蓄积(立方米)' />
                </el-form-item>
                <el-form-item label='本年度减少_非林业经营活动_有害生物_面积(公顷)' prop='cyrnfopAreaHectares'>
                    <el-input v-model='form.cyrnfopAreaHectares' placeholder='请输入本年度减少_非林业经营活动_有害生物_面积(公顷)' />
                </el-form-item>
                <el-form-item label='本年度减少_非林业经营活动_有害生物_蓄积(立方米)' prop='cyrnfopVolumeCubicMeters'>
                    <el-input v-model='form.cyrnfopVolumeCubicMeters' placeholder='请输入本年度减少_非林业经营活动_有害生物_蓄积(立方米)' />
                </el-form-item>
                <el-form-item label='本年度减少_非林业经营活动_其他_面积(公顷)' prop='cyrnfooAreaHectares'>
                    <el-input v-model='form.cyrnfooAreaHectares' placeholder='请输入本年度减少_非林业经营活动_其他_面积(公顷)' />
                </el-form-item>
                <el-form-item label='本年度减少_非林业经营活动_其他_蓄积(立方米)' prop='cyrnfooVolumeCubicMeters'>
                    <el-input v-model='form.cyrnfooVolumeCubicMeters' placeholder='请输入本年度减少_非林业经营活动_其他_蓄积(立方米)' />
                </el-form-item>
                <el-form-item label='本年度减少_自然枯损_蓄积(立方米)' prop='cynlVolumeCubicMeters'>
                    <el-input v-model='form.cynlVolumeCubicMeters' placeholder='请输入本年度减少_自然枯损_蓄积(立方米)' />
                </el-form-item>
                <el-form-item label='本年度净增值' prop='cyniAreaHectares'>
                    <el-input v-model='form.cyniAreaHectares' placeholder='请输入本年度净增值' />
                </el-form-item>
                <el-form-item label='本年度净增值' prop='cyniVolumeCubicMeters'>
                    <el-input v-model='form.cyniVolumeCubicMeters' placeholder='请输入本年度净增值' />
                </el-form-item>
                <el-form-item label='修正值' prop='correctionsAreaHectares'>
                    <el-input v-model='form.correctionsAreaHectares' placeholder='请输入修正值' />
                </el-form-item>
                <el-form-item label='修正值' prop='cvolumeCubicMeters'>
                    <el-input v-model='form.cvolumeCubicMeters' placeholder='请输入修正值' />
                </el-form-item>
                <el-form-item label='本年度实有_面积(公顷)' prop='cyacAreaHectares'>
                    <el-input v-model='form.cyacAreaHectares' placeholder='请输入本年度实有_面积(公顷)' />
                </el-form-item>
                <el-form-item label='本年度实有_蓄积(立方米)' prop='cyacVolumeCubicMeters'>
                    <el-input v-model='form.cyacVolumeCubicMeters' placeholder='请输入本年度实有_蓄积(立方米)' />
                </el-form-item>
                <el-form-item label='状态|字典值for_sta_apply_status' prop='forStaApplyStatus'>
                    <el-input v-model='form.forStaApplyStatus' placeholder='请输入状态|字典值for_sta_apply_status' />
                </el-form-item>
                <el-form-item label='备注' prop='remark'>
                    <el-input v-model='form.remark' placeholder='请输入备注' />
                </el-form-item>
                <el-form-item label='生效状态 1：生效，2：失效' prop='statusCd'>
                    <el-input v-model='form.statusCd' placeholder='请输入生效状态 1：生效，2：失效' />
                </el-form-item>
            </el-form>
            <template #footer>
                <div slot='footer' class='dialog-footer'>
                    <el-button type='primary' @click='submitForm'>确 定</el-button>
                    <el-button @click='cancel'>取 消</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script>
    import "@/views/sysforestresource/assets/styles/index.scss";
    import {
        listForAnnualForestResStatistics,
        getForAnnualForestResStatistics,
        delForAnnualForestResStatistics,
        addForAnnualForestResStatistics,
        updateForAnnualForestResStatistics,
        delForAnnualForestResStatisticss,
        dataSubmit,
        dataBack
    } from '@/api/sysforestresource/forest/ForAnnualForestResStatistics'
    import { queryOrgTreeByUserOrg } from '@/api/sysforestresource/login'
    import { getDicts } from "@/api/sysforestresource/dict";
    import { selectDictLabel } from "@/api/sysforestresource/utils/cop";
    import exceldownload from '@/views/sysforestresource/components/exceldownload'
    import FileUpload1 from '@/views/sysforestresource/components/FileUpload1'

    export default {
        name: '/forest/ForAnnualForestResStatistics/queryByPage',
        components: {
            exceldownload,
            FileUpload1,
        },
        data() {
            return {
                // 遮罩层
                loading: true,
                // 选中数组
                ids: [],
                // 非单个禁用
                single: true,
                // 非多个禁用
                multiple: true,
                // 显示搜索条件
                showSearch: true,
                // 总条数
                total: 0,
                // 年度森林资源统计表表格数据
                ForAnnualForestResStatisticsList: [],
                // 弹出层标题
                title: '',
                // 是否显示弹出层
                open: false,
                // 查询参数
                queryParams: {
                    page: 1,
                    rows: 10,
                    forStatisticsId: null,
                    orgCode: null,
                    orgName: null,
                    yearNo: null,
                    forestOwnershipCategory: null,
                    pyacAreaHectares: null,
                    pyacVolumeCubicMeters: null,
                    nfaTotalHectares: null,
                    nfapfForestryHectares: null,
                    nfaTraceUpdatedHectares: null,
                    nfacmForestryHectares: null,
                    nfVolumeCubicMeters: null,
                    tgVolumeCubicMeters: null,
                    cyrtAreaHectares: null,
                    cyrtVolumeCubicMeters: null,
                    cyrfosAreaHectares: null,
                    cyrfosVolumeCubicMeters: null,
                    cyrfolAreaHectares: null,
                    cyrfolVolumeCubicMeters: null,
                    cyrfolvaAreaHectares: null,
                    cyrfolvaVolumeCubicMeters: null,
                    cyrnfopsAreaHectares: null,
                    cyrnfosVolumeCubicMeters: null,
                    cyrnfodlAreaHectares: null,
                    cyrnfodlVolumeCubicMeters: null,
                    cyrnfodAreaHectares: null,
                    cyrnfodVolumeCubicMeters: null,
                    cyrnfofAreaHectares: null,
                    cyrnfofVolumeCubicMeters: null,
                    cyrnfopAreaHectares: null,
                    cyrnfopVolumeCubicMeters: null,
                    cyrnfooAreaHectares: null,
                    cyrnfooVolumeCubicMeters: null,
                    cynlVolumeCubicMeters: null,
                    cyniAreaHectares: null,
                    cyniVolumeCubicMeters: null,
                    correctionsAreaHectares: null,
                    cvolumeCubicMeters: null,
                    cyacAreaHectares: null,
                    cyacVolumeCubicMeters: null,
                    forStaApplyStatus: null,
                    statusCd: null,
                },
                // 表单参数
                form: {},
                // 表单校验
                rules: {},

                url:
                    import.meta.env.VITE_APP_BASE_API +
                    '/bdh-forest-resource-api/forest/ForAnnualForestResStatistics/exportTemplate',
                uploadFileUrl:
                    import.meta.env.VITE_APP_BASE_API + '/bdh-forest-resource-api/forest/ForAnnualForestResStatistics/importExcel',
                orgTreeList: [],
                forStaApplyStatusOptions: [],
                forestOwnershipCategoryOptions: [],
                tableHeight: 200,
                searchHeight: 0,
            }
        },
        created() {
            this.getList()
            this.getOrgTreeByUserOrg()

            getDicts('for_sta_apply_status').then((response) => {
                this.forStaApplyStatusOptions = response.data
            })
            getDicts('forest_ownership_category').then((response) => {
                this.forestOwnershipCategoryOptions = response.data
            })
        },
        mounted() {
            this.searchHeight = this.$refs.searchDom.clientHeight;
            this.tableHeight = this.showSearch
                ? window.innerHeight - this.searchHeight - 220
                : window.innerHeight - 220
        },
        watch: {
            //搜索隐藏调整表格大小
            showSearch() {
                this.tableHeight = this.showSearch
                    ? window.innerHeight - this.searchHeight - 220
                    : window.innerHeight - 220;
            },
        },
        methods: {
            objectSpanMethod({ row, column, rowIndex, columnIndex }) {
                if (columnIndex === 1) {
                    const rows = this.ForAnnualForestResStatisticsList
                    const prevRow = rows[rowIndex - 1]
                    let nextRow = rows[rowIndex + 1]
                    if (prevRow && prevRow.orgCode === row.orgCode) {
                        return {
                            rowspan: 0,
                            colspan: 1,
                        }
                    } else {
                        let rowspan = 1
                        while (nextRow && nextRow.orgCode === row.orgCode) {
                            rowspan += 1
                            nextRow = rows[rowIndex + rowspan]
                        }
                        return {
                            rowspan,
                            colspan: 1,
                        }
                    }
                }
                if (columnIndex === 2) {
                    const rows = this.ForAnnualForestResStatisticsList
                    const prevRow = rows[rowIndex - 1]
                    let nextRow = rows[rowIndex + 1]
                    if (prevRow && prevRow.orgCode === row.orgCode && prevRow.yearNo === row.yearNo) {
                        return {
                            rowspan: 0,
                            colspan: 1,
                        }
                    } else {
                        let rowspan = 1
                        while (nextRow && nextRow.orgCode === row.orgCode && nextRow.yearNo === row.yearNo) {
                            rowspan += 1
                            nextRow = rows[rowIndex + rowspan]
                        }
                        return {
                            rowspan,
                            colspan: 1,
                        }
                    }
                }
            },

            forStaApplyStatusFormat(row, column) {
                return selectDictLabel(this.forStaApplyStatusOptions, row.forStaApplyStatus != null && row.forStaApplyStatus != undefined ? row.forStaApplyStatus.toString() : '')
            },
            forestOwnershipCategoryFormat(row, column) {
                if (row.forestOwnershipCategory == '11') {
                    return '总计'
                }
                if (row.forestOwnershipCategory == '12') {
                    return '国有合计'
                }
                if (row.forestOwnershipCategory == '13') {
                    return '集体合计'
                }
                if (row.forestOwnershipCategory == '14') {
                    return '个人合计'
                }
                if (row.forestOwnershipCategory == '15') {
                    return '其他合计'
                }
                return selectDictLabel(this.forestOwnershipCategoryOptions, row.forestOwnershipCategory != null && row.forestOwnershipCategory != undefined ? row.forestOwnershipCategory.toString() : '')
            },


            async getOrgTreeByUserOrg() {
                let res = await queryOrgTreeByUserOrg()
                let { data, code } = res
                this.orgTreeList = data.children


            },
            farmHandleChangeTop(value) {
                var node = this.$refs.farmSelectTop.getCheckedNodes()[0]
                if (node != null) {
                    let data = node.data
                    this.queryParams.orgCode = data.orgNo
                } else {
                    this.queryParams.orgCode = null
                }

                if (this.$refs.farmSelectTop) {
                    this.$refs.farmSelectTop.togglePopperVisible() //监听值发生变化就关闭它
                }
            },

            /** 查询年度森林资源统计表列表 */
            getList() {
                this.loading = true
                listForAnnualForestResStatistics(this.queryParams).then(response => {
                    this.ForAnnualForestResStatisticsList = response.data.records
                    this.total = response.data.total
                    this.loading = false
                })
            },
            // 取消按钮
            cancel() {
                this.open = false
                this.reset()
            },
            // 表单重置
            reset() {
                this.form = {
                    forStatisticsId: null,
                    orgCode: null,
                    orgName: null,
                    yearNo: null,
                    forestOwnershipCategory: null,
                    pyacAreaHectares: null,
                    pyacVolumeCubicMeters: null,
                    nfaTotalHectares: null,
                    nfapfForestryHectares: null,
                    nfaTraceUpdatedHectares: null,
                    nfacmForestryHectares: null,
                    nfVolumeCubicMeters: null,
                    tgVolumeCubicMeters: null,
                    cyrtAreaHectares: null,
                    cyrtVolumeCubicMeters: null,
                    cyrfosAreaHectares: null,
                    cyrfosVolumeCubicMeters: null,
                    cyrfolAreaHectares: null,
                    cyrfolVolumeCubicMeters: null,
                    cyrfolvaAreaHectares: null,
                    cyrfolvaVolumeCubicMeters: null,
                    cyrnfopsAreaHectares: null,
                    cyrnfosVolumeCubicMeters: null,
                    cyrnfodlAreaHectares: null,
                    cyrnfodlVolumeCubicMeters: null,
                    cyrnfodAreaHectares: null,
                    cyrnfodVolumeCubicMeters: null,
                    cyrnfofAreaHectares: null,
                    cyrnfofVolumeCubicMeters: null,
                    cyrnfopAreaHectares: null,
                    cyrnfopVolumeCubicMeters: null,
                    cyrnfooAreaHectares: null,
                    cyrnfooVolumeCubicMeters: null,
                    cynlVolumeCubicMeters: null,
                    cyniAreaHectares: null,
                    cyniVolumeCubicMeters: null,
                    correctionsAreaHectares: null,
                    cvolumeCubicMeters: null,
                    cyacAreaHectares: null,
                    cyacVolumeCubicMeters: null,
                    forStaApplyStatus: null,
                    remark: null,
                    createBy: null,
                    createTime: null,
                    updateBy: null,
                    updateTime: null,
                    statusCd: null,
                }
                this.resetForm('formRef')
            },
            /** 搜索按钮操作 */
            handleQuery() {
                this.queryParams.page = 1
                this.getList()
            },
            /** 重置按钮操作 */
            resetQuery() {
                this.resetForm('queryForm')
                this.handleQuery()
            },
            // 多选框选中数据
            handleSelectionChange(selection) {
                this.ids = selection.map(item => item.forStatisticsId)
                this.single = selection.length !== 1
                this.multiple = !selection.length
            },
            /** 新增按钮操作 */
            handleAdd() {
                this.reset()
                this.open = true
                this.title = '添加年度森林资源统计表'
            },
            /** 修改按钮操作 */
            handleUpdate(row) {
                this.reset()
                const forStatisticsId = row.forStatisticsId || this.ids
                getForAnnualForestResStatistics(forStatisticsId).then(response => {
                    this.form = response.data
                    this.open = true
                    this.title = '修改年度森林资源统计表'
                })
            },
            /** 提交按钮 */
            submitForm() {
                this.$refs['formRef'].validate(valid => {
                    if (valid) {
                        if (this.form.forStatisticsId != null) {
                            updateForAnnualForestResStatistics(this.form).then(response => {
                                this.$message({
                                    message: '修改成功',
                                    type: 'success'
                                })
                                this.open = false
                                this.getList()
                            })
                        } else {
                            addForAnnualForestResStatistics(this.form).then(response => {
                                this.$message({
                                    message: '新增成功',
                                    type: 'success'
                                })
                                this.open = false
                                this.getList()
                            })
                        }
                    }
                })
            },
            /** 删除按钮操作 */
            handleDelete(row) {
                const forStatisticsIds = row.forStatisticsId || this.ids
                this.$confirm('是否确认删除年度森林资源统计表编号为"' + forStatisticsIds + '"的数据项?', '警告', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning',
                }).then(function () {
                    return Array.isArray(forStatisticsIds) ? delForAnnualForestResStatisticss(forStatisticsIds) : delForAnnualForestResStatistics(forStatisticsIds)
                }).then(() => {
                    this.getList()
                    this.$message({
                        message: '删除成功',
                        type: 'success'
                    })
                })
            },
            handleDataSubmit(row) {
                const forStatisticsIds = row.forStatisticsId || this.ids
                this.$confirm('是否确认提交数据', '警告', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning',
                }).then(function () {
                    return dataSubmit({ forStatisticsId: forStatisticsIds })
                }).then(() => {
                    this.getList()
                    this.$message({
                        message: '提交成功',
                        type: 'success'
                    })
                })
            },
            handleDataBack(row) {
                const forStatisticsIds = row.forStatisticsId || this.ids
                this.$confirm('是否确认退回数据', '警告', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning',
                }).then(function () {
                    return dataBack({ forStatisticsId: forStatisticsIds })
                }).then(() => {
                    this.getList()
                    this.$message({
                        message: '退回成功',
                        type: 'success'
                    })
                })
            },
        },
    }
</script>
