<template>
    <div class='app-container'>
        <div ref="searchDom">
            <el-collapse-transition>
                <el-form :model='queryParams' class='queryClass form-line' ref='queryForm' :inline='true'
                    v-show='showSearch' label-width='68px'>
                    <el-row :gutter="20">
                        <el-col :span="6">
                            <el-form-item label='单位' prop='orgCode'>
                                <el-cascader ref='farmSelectTop' placeholder='请选择单位' v-model='queryParams.orgCode'
                                    @clear='queryParams.orgCode = null; queryParams.orgCode = null'
                                    :options='orgTreeList' :props="{
                                    value: 'orgNo',
                                    label: 'orgName',
                                    expandTrigger: 'hover',
                                    checkStrictly: true,
                                    emitPath: false,
                                }" clearable @change='farmHandleChangeTop'>
                                </el-cascader>
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label='年度' prop='yearNo'>
                                <el-date-picker v-model='queryParams.yearNo' value-format='YYYY' type='year' clearable
                                    placeholder='请选择年度'>
                                </el-date-picker>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item>
                                <el-button type='primary' icon='Search' @click='handleQuery'>搜索</el-button>
                                <el-button icon='Refresh' @click='resetQuery'>重置</el-button>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
            </el-collapse-transition>
        </div>

        <el-row :gutter='10' class='mb8 plotClass'>
            <!-- <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
        >新增</el-button>
      </el-col> -->
            <!-- <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
        >修改</el-button>
      </el-col> -->
            <!-- <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"

          :disabled="multiple"
          @click="handleDelete"
        >删除</el-button>
      </el-col> -->
            <el-col :span='1.5' v-hasPermi="['ForGrasslandResAreaStats:exportTemplate']">
                <exceldownload ref='exceldownload' :param='queryParams' :url='url'
                    v-hasPermi="['ForGrasslandResAreaStats:exportTemplate']"></exceldownload>
            </el-col>
            <el-col :span='1.5' v-hasPermi="['ForGrasslandResAreaStats:importExcel']">
                <FileUpload1 :isShowTip='false' :default="['xlsx']" :text1="'导入'" :uploadFileUrl='uploadFileUrl'
                    @newlist='getList' v-hasPermi="['ForGrasslandResAreaStats:importExcel']"></FileUpload1>
            </el-col>
            <right-toolbar v-model:showSearch="showSearch" @queryTable='getList'></right-toolbar>
        </el-row>

        <el-table :height="tableHeight" :data='ForGrasslandResAreaStatsList' @selection-change='handleSelectionChange'>
            <el-table-column type='selection' width='55' align='center' fixed='left' />
            <el-table-column label='单位' align='center' prop='orgName' fixed='left' />
            <el-table-column label='年度' align='center' prop='yearNo' fixed='left' />
            <el-table-column label='面积合计' align='center' prop='totalArea' />
            <el-table-column label='天然草地' align='center'>
                <el-table-column label='小计' align='center' prop='newForestAreaSubtotal' />
                <el-table-column label='国有' align='center' prop='newForestAreaState' />
                <el-table-column label='集体' align='center' prop='newForestAreaCollective' />
            </el-table-column>
            <el-table-column label='人工草地' align='center'>
                <el-table-column label='小计' align='center' prop='artificialGrasslandSubtotal' />
                <el-table-column label='国有' align='center' prop='artificialGrasslandState' />
                <el-table-column label='集体' align='center' prop='artificialGrasslandCollective' />
            </el-table-column>
            <el-table-column label='其他草地' align='center'>
                <el-table-column label='小计' align='center' prop='otherGrasslandSubtotal' />
                <el-table-column label='国有' align='center' prop='otherGrasslandState' />
                <el-table-column label='集体' align='center' prop='otherGrasslandCollective' />
            </el-table-column>
            <el-table-column label='草原综合植被盖度(%)' align='center' prop='grasslandVegetationCoverage' />
            <!--      <el-table-column label="草原覆盖度(%)" align="center" prop="grasslandCoverage" />-->
            <el-table-column label='状态' align='center' prop='forGrasslandStatus' fixed='right'
                :formatter='forGrasslandStatusFormat' />
            <!-- <el-table-column label="备注" align="center" prop="remark" /> -->
            <el-table-column label='操作' align='center' class-name='small-padding fixed-width' fixed='right'>
                <template #default="scope">
                    <!--          <el-button  type="primary" icon="Edit" @click="handleUpdate(scope.row)">修改</el-button>-->
                    <!--          <el-button  type="primary" icon="Delete" @click="handleDelete(scope.row)">删除</el-button>-->
                    <template v-if='scope.row.forGrasslandStatus == "3"'>
                        <el-button link type='primary' v-hasPermi="['ForGrasslandResAreaStats:submit']"
                            @click='handleDataSubmit(scope.row)'>提交
                        </el-button>
                    </template>
                    <template v-if='scope.row.forGrasslandStatus == "2"'>
                        <el-button link type='primary' v-hasPermi="['ForGrasslandResAreaStats:back']"
                            @click='handleDataBack(scope.row)'>退回
                        </el-button>
                    </template>
                </template>
            </el-table-column>
        </el-table>

        <pagination v-show='total > 0' :total='total' v-model:page='queryParams.page' v-model:limit='queryParams.rows'
            @pagination='getList' />

        <!-- 添加或修改草原资源面积统计表对话框 -->
        <el-dialog :title='title' v-model='open' width='500px' append-to-body>
            <el-form ref='formRef' :model='form' :rules='rules' label-width='80px'>
                <el-form-item label='主键id' prop='forGrasslandStatId'>
                    <el-input v-model='form.forGrasslandStatId' placeholder='请输入主键id' />
                </el-form-item>
                <el-form-item label='org_code 组织机构编码' prop='orgCode'>
                    <el-input v-model='form.orgCode' placeholder='请输入org_code 组织机构编码' />
                </el-form-item>
                <el-form-item label='org_name 组织机构名称' prop='orgName'>
                    <el-input v-model='form.orgName' placeholder='请输入org_name 组织机构名称' />
                </el-form-item>
                <el-form-item label='year_no 年度' prop='yearNo'>
                    <el-input v-model='form.yearNo' placeholder='请输入year_no 年度' />
                </el-form-item>
                <el-form-item label='面积合计' prop='totalArea'>
                    <el-input v-model='form.totalArea' placeholder='请输入面积合计' />
                </el-form-item>
                <el-form-item label='天然草地_小计' prop='newForestAreaSubtotal'>
                    <el-input v-model='form.newForestAreaSubtotal' placeholder='请输入新增有林地面积_小计' />
                </el-form-item>
                <el-form-item label='天然草地_国有' prop='newForestAreaState'>
                    <el-input v-model='form.newForestAreaState' placeholder='请输入新增有林地面积_国有' />
                </el-form-item>
                <el-form-item label='天然草地_集体' prop='newForestAreaCollective'>
                    <el-input v-model='form.newForestAreaCollective' placeholder='请输入新增有林地面积_集体' />
                </el-form-item>
                <el-form-item label='人工草地_小计' prop='artificialGrasslandSubtotal'>
                    <el-input v-model='form.artificialGrasslandSubtotal' placeholder='请输入人工草地_小计' />
                </el-form-item>
                <el-form-item label='人工草地_国有' prop='artificialGrasslandState'>
                    <el-input v-model='form.artificialGrasslandState' placeholder='请输入人工草地_国有' />
                </el-form-item>
                <el-form-item label='人工草地_集体' prop='artificialGrasslandCollective'>
                    <el-input v-model='form.artificialGrasslandCollective' placeholder='请输入人工草地_集体' />
                </el-form-item>
                <el-form-item label='其他草地_小计' prop='otherGrasslandSubtotal'>
                    <el-input v-model='form.otherGrasslandSubtotal' placeholder='请输入其他草地_小计' />
                </el-form-item>
                <el-form-item label='其他草地_国有' prop='otherGrasslandState'>
                    <el-input v-model='form.otherGrasslandState' placeholder='请输入其他草地_国有' />
                </el-form-item>
                <el-form-item label='其他草地_集体' prop='otherGrasslandCollective'>
                    <el-input v-model='form.otherGrasslandCollective' placeholder='请输入其他草地_集体' />
                </el-form-item>
                <el-form-item label='草原综合植被盖度' prop='grasslandVegetationCoverage'>
                    <el-input v-model='form.grasslandVegetationCoverage' placeholder='请输入草原综合植被盖度' />
                </el-form-item>
                <el-form-item label='草原覆盖度' prop='grasslandCoverage'>
                    <el-input v-model='form.grasslandCoverage' placeholder='请输入草原覆盖度' />
                </el-form-item>
                <el-form-item label='状态|字典值apply_status' prop='forGrasslandStatus'>
                    <el-input v-model='form.forGrasslandStatus' placeholder='请输入状态|字典值apply_status' />
                </el-form-item>
                <el-form-item label='备注' prop='remark'>
                    <el-input v-model='form.remark' placeholder='请输入备注' />
                </el-form-item>
                <el-form-item label='生效状态 1：生效，2：失效' prop='statusCd'>
                    <el-input v-model='form.statusCd' placeholder='请输入生效状态 1：生效，2：失效' />
                </el-form-item>
            </el-form>
            <template #footer>
                <div slot='footer' class='dialog-footer'>
                    <el-button type='primary' @click='submitForm'>确 定</el-button>
                    <el-button @click='cancel'>取 消</el-button>
                </div>
            </template>
        </el-dialog>


    </div>
</template>

<script>
    import { getDicts } from "@/api/sysforestresource/dict";
    import "@/views/sysforestresource/assets/styles/index.scss";
    import {
        addForGrasslandResAreaStats,
        dataBack,
        dataSubmit,
        delForGrasslandResAreaStats,
        delForGrasslandResAreaStatss,
        getForGrasslandResAreaStats,
        listForGrasslandResAreaStats,
        updateForGrasslandResAreaStats,
    } from '@/api/sysforestresource/forest/ForGrasslandResAreaStats'
    import { queryOrgTreeByUserOrg } from '@/api/sysforestresource/login'
    import { selectDictLabel } from "@/api/sysforestresource/utils/cop";
    import exceldownload from '@/views/sysforestresource/components/exceldownload'
    import FileUpload1 from '@/views/sysforestresource/components/FileUpload1'

    export default {
        name: '/forest/ForGrasslandResAreaStats/queryByPage',
        components: {
            exceldownload,
            FileUpload1,
        },
        data() {
            return {
                // 遮罩层
                loading: true,
                // 选中数组
                ids: [],
                // 非单个禁用
                single: true,
                // 非多个禁用
                multiple: true,
                // 显示搜索条件
                showSearch: true,
                // 总条数
                total: 0,
                // 草原资源面积统计表表格数据
                ForGrasslandResAreaStatsList: [],
                // 弹出层标题
                title: '',
                // 是否显示弹出层
                open: false,
                // 查询参数
                queryParams: {
                    page: 1,
                    rows: 10,
                    forGrasslandStatId: null,
                    orgCode: null,
                    orgName: null,
                    yearNo: null,
                    totalArea: null,
                    newForestAreaSubtotal: null,
                    newForestAreaState: null,
                    newForestAreaCollective: null,
                    artificialGrasslandSubtotal: null,
                    artificialGrasslandState: null,
                    artificialGrasslandCollective: null,
                    otherGrasslandSubtotal: null,
                    otherGrasslandState: null,
                    otherGrasslandCollective: null,
                    grasslandVegetationCoverage: null,
                    grasslandCoverage: null,
                    forGrasslandStatus: null,
                    statusCd: null,
                },
                // 表单参数
                form: {},
                // 表单校验
                rules: {},
                url:
                    import.meta.env.VITE_APP_BASE_API +
                    '/bdh-forest-resource-api/forest/ForGrasslandResAreaStats/exportTemplate',
                uploadFileUrl:
                    import.meta.env.VITE_APP_BASE_API + '/bdh-forest-resource-api/forest/ForGrasslandResAreaStats/importExcel',
                orgTreeList: [],
                tableHeight: 200,
                forGrasslandStatusOptions: [],
                searchHeight: 0,
            }
        },
        created() {
            this.getList()
            this.getOrgTreeByUserOrg()

            getDicts('for_sta_apply_status').then((response) => {
                this.forGrasslandStatusOptions = response.data
            })
        },
        mounted() {
            this.searchHeight = this.$refs.searchDom.clientHeight;
            this.tableHeight = this.showSearch
                ? window.innerHeight - this.searchHeight - 220
                : window.innerHeight - 220
        },
        watch: {
            //搜索隐藏调整表格大小
            showSearch() {
                this.tableHeight = this.showSearch
                    ? window.innerHeight - this.searchHeight - 220
                    : window.innerHeight - 220;
            },
        },
        methods: {

            forGrasslandStatusFormat(row, column) {
                return selectDictLabel(this.forGrasslandStatusOptions, row.forGrasslandStatus != null && row.forGrasslandStatus != undefined ? row.forGrasslandStatus.toString() : '')
            },

            async getOrgTreeByUserOrg() {
                let res = await queryOrgTreeByUserOrg()
                let { data, code } = res
                this.orgTreeList = data.children
            },

            farmHandleChangeTop(value) {
                var node = this.$refs.farmSelectTop.getCheckedNodes()[0]
                if (node != null) {
                    let data = node.data
                    this.queryParams.orgCode = data.orgNo
                } else {
                    this.queryParams.orgCode = null
                }

                if (this.$refs.farmSelectTop) {
                    this.$refs.farmSelectTop.togglePopperVisible() //监听值发生变化就关闭它
                }
            },

            /** 查询草原资源面积统计表列表 */
            getList() {
                this.loading = true
                listForGrasslandResAreaStats(this.queryParams).then(response => {
                    this.ForGrasslandResAreaStatsList = response.data.records
                    this.total = response.data.total
                    this.loading = false
                })
            },
            // 取消按钮
            cancel() {
                this.open = false
                this.reset()
            },
            // 表单重置
            reset() {
                this.form = {
                    forGrasslandStatId: null,
                    orgCode: null,
                    orgName: null,
                    yearNo: null,
                    totalArea: null,
                    newForestAreaSubtotal: null,
                    newForestAreaState: null,
                    newForestAreaCollective: null,
                    artificialGrasslandSubtotal: null,
                    artificialGrasslandState: null,
                    artificialGrasslandCollective: null,
                    otherGrasslandSubtotal: null,
                    otherGrasslandState: null,
                    otherGrasslandCollective: null,
                    grasslandVegetationCoverage: null,
                    grasslandCoverage: null,
                    forGrasslandStatus: null,
                    remark: null,
                    createBy: null,
                    createTime: null,
                    updateBy: null,
                    updateTime: null,
                    statusCd: null,
                }
                this.resetForm('formRef')
            },
            /** 搜索按钮操作 */
            handleQuery() {
                this.queryParams.page = 1
                this.getList()
            },
            /** 重置按钮操作 */
            resetQuery() {
                this.resetForm('queryForm')
                this.handleQuery()
            },
            // 多选框选中数据
            handleSelectionChange(selection) {
                this.ids = selection.map(item => item.forGrasslandStatId)
                this.single = selection.length !== 1
                this.multiple = !selection.length
            },
            /** 新增按钮操作 */
            handleAdd() {
                this.reset()
                this.open = true
                this.title = '添加草原资源面积统计表'
            },
            /** 修改按钮操作 */
            handleUpdate(row) {
                this.reset()
                const forGrasslandStatId = row.forGrasslandStatId || this.ids
                getForGrasslandResAreaStats(forGrasslandStatId).then(response => {
                    this.form = response.data
                    this.open = true
                    this.title = '修改草原资源面积统计表'
                })
            },
            /** 提交按钮 */
            submitForm() {
                this.$refs['formRef'].validate(valid => {
                    if (valid) {
                        if (this.form.forGrasslandStatId != null) {
                            updateForGrasslandResAreaStats(this.form).then(response => {
                                this.$message({
                                    message: '修改成功',
                                    type: 'success'
                                })
                                this.open = false
                                this.getList()
                            })
                        } else {
                            addForGrasslandResAreaStats(this.form).then(response => {
                                this.$message({
                                    message: '新增成功',
                                    type: 'success'
                                })
                                this.open = false
                                this.getList()
                            })
                        }
                    }
                })
            },
            /** 删除按钮操作 */
            handleDelete(row) {
                const forGrasslandStatIds = row.forGrasslandStatId || this.ids
                this.$confirm('是否确认删除草原资源面积统计表编号为"' + forGrasslandStatIds + '"的数据项?', '警告', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning',
                }).then(function () {
                    return Array.isArray(forGrasslandStatIds) ? delForGrasslandResAreaStatss(forGrasslandStatIds) : delForGrasslandResAreaStats(forGrasslandStatIds)
                }).then(() => {
                    this.getList()
                    this.$message({
                        message: '删除成功',
                        type: 'success'
                    })
                })
            },
            handleDataSubmit(row) {
                const forGrasslandStatIds = row.forGrasslandStatId || this.ids
                this.$confirm('是否确认提交数据', '警告', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning',
                }).then(function () {
                    return dataSubmit({ forGrasslandStatId: forGrasslandStatIds })
                }).then(() => {
                    this.getList()
                    this.$message({
                        message: '提交成功',
                        type: 'success'
                    })
                })
            },
            handleDataBack(row) {
                const forGrasslandStatIds = row.forGrasslandStatId || this.ids
                this.$confirm('是否确认退回数据', '警告', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning',
                }).then(function () {
                    return dataBack({ forGrasslandStatId: forGrasslandStatIds })
                }).then(() => {
                    this.getList()
                    this.$message({
                        message: '退回成功',
                        type: 'success'
                    })
                })
            },
        },
    }


</script>
