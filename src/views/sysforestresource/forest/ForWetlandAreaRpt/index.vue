<template>
    <div class='app-container'>
        <div ref="searchDom">
            <el-collapse-transition>
                <el-form :model='queryParams' class='queryClass form-line' ref='queryForm' :inline='true'
                    v-show='showSearch' label-width='68px'>
                    <el-row :gutter="20">
                        <el-col :span="6">
                            <el-form-item label='单位' prop='orgCode'>
                                <el-cascader ref='farmSelectTop' style='width: 400px' placeholder='请选择单位'
                                    v-model='queryParams.orgCode'
                                    @clear='queryParams.orgCode = null; queryParams.orgCode = null'
                                    :options='orgTreeList' :props="{
                    value: 'orgNo',
                    label: 'orgName',
                    expandTrigger: 'hover',
                    checkStrictly: true,
                    emitPath: false,
                  }" size='medium' clearable @change='farmHandleChangeTop'>
                                </el-cascader>
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label='年度' prop='statYear'>
                                <el-date-picker v-model='queryParams.statYear' value-format='YYYY' type='year' clearable
                                    placeholder='请选择年度'>
                                </el-date-picker>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item>
                                <el-button type='primary' icon='Search' @click='handleQuery'>搜索</el-button>
                                <el-button icon='Refresh' @click='resetQuery'>重置</el-button>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
            </el-collapse-transition>
        </div>
        <el-row :gutter='10' class='mb8 plotClass'>
            <!-- <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"

        >新增</el-button>
      </el-col> -->
            <!-- <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"

        >修改</el-button>
      </el-col> -->
            <!-- <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"

          :disabled="multiple"
          @click="handleDelete"

        >删除</el-button>
      </el-col> -->

            <el-col :span='1.5' v-hasPermi="['ForWetlandAreaRpt:exportTemplate']">
                <exceldownload ref='exceldownload' :param='queryParams' :url='url'
                    v-hasPermi="['ForWetlandAreaRpt:exportTemplate']"></exceldownload>
            </el-col>

            <el-col :span='1.5' v-hasPermi="['ForWetlandAreaRpt:importExcel']">
                <FileUpload1 :isShowTip='false' :default="['xlsx']" :text1="'导入'" :uploadFileUrl='uploadFileUrl'
                    @newlist='getList' v-hasPermi="['ForWetlandAreaRpt:importExcel']"></FileUpload1>
            </el-col>

            <right-toolbar v-model:showSearch="showSearch" @queryTable='getList'></right-toolbar>
        </el-row>

        <el-table :height="tableHeight" :data='ForWetlandAreaRptList' @selection-change='handleSelectionChange'>
            <el-table-column type='selection' width='55' align='center' />
            <el-table-column label='单位' align='center' prop='orgName' />
            <el-table-column label='年度' align='center' prop='statYear' />
            <el-table-column label='合计' align='center' prop='totalNum' />
            <el-table-column label='森林沼泽' align='center' prop='forSwampNum' />
            <el-table-column label='灌丛沼泽' align='center' prop='shrubSwampNum' />
            <el-table-column label='沼泽草地' align='center' prop='swampGrassNum' />
            <el-table-column label='其他沼泽地' align='center' prop='otherSwampNum' />
            <el-table-column label='沿海滩涂' align='center' prop='coastalMudflatNum' />
            <el-table-column label='内陆滩涂' align='center' prop='inlandMudflatNum' />
            <el-table-column label='红树林' align='center' prop='mangroveNum' />
            <!-- <el-table-column label="备注" align="center" prop="remark" /> -->
            <el-table-column label='数态' align='center' prop='dataStatus' :formatter='dataStatusFormat' />
            <el-table-column label='操作' align='center' fixed="right" class-name='small-padding fixed-width'>
                <template #default="scope">
                    <!--          <el-button-->
                    <!--            icon="Edit"-->
                    <!--            @click="handleUpdate(scope.row)"-->
                    <!--          >修改</el-button>-->
                    <!--          <el-button-->
                    <!--            icon="Delete"-->
                    <!--            @click="handleDelete(scope.row)"-->
                    <!--          >删除</el-button>-->
                    <template v-if='scope.row.dataStatus == "3"'>
                        <el-button link type='primary' v-hasPermi="['ForWetlandAreaRpt:submit']"
                            @click='handleDataSubmit(scope.row)'>提交
                        </el-button>
                    </template>
                    <template v-if='scope.row.dataStatus == "2"'>
                        <el-button link type='primary' v-hasPermi="['ForWetlandAreaRpt:back']"
                            @click='handleDataBack(scope.row)'>退回
                        </el-button>
                    </template>
                </template>
            </el-table-column>
        </el-table>

        <pagination v-show='total>0' :total='total' v-model:page='queryParams.page' v-model:limit='queryParams.rows'
            @pagination='getList' />

        <!-- 添加或修改各类湿地面积统计表对话框 -->
        <el-dialog :title='title' v-model='open' width='500px' append-to-body>
            <el-form ref='formRef' :model='form' :rules='rules' label-width='80px'>
                <el-form-item label='单位' prop='orgName'>
                    <el-input v-model='form.orgName' placeholder='请输入组织机构名称' />
                </el-form-item>
                <el-form-item label='年度' prop='statYear'>
                    <el-input v-model='form.statYear' placeholder='请输入年份' />
                </el-form-item>
                <el-form-item label='合计' prop='totalNum'>
                    <el-input v-model='form.totalNum' placeholder='请输入合计' />
                </el-form-item>
                <el-form-item label='森林沼泽' prop='forSwampNum'>
                    <el-input v-model='form.forSwampNum' placeholder='请输入森林沼泽' />
                </el-form-item>
                <el-form-item label='灌丛沼泽' prop='shrubSwampNum'>
                    <el-input v-model='form.shrubSwampNum' placeholder='请输入灌丛沼泽' />
                </el-form-item>
                <el-form-item label='沼泽草地' prop='swampGrassNum'>
                    <el-input v-model='form.swampGrassNum' placeholder='请输入沼泽草地' />
                </el-form-item>
                <el-form-item label='其他沼泽地' prop='otherSwampNum'>
                    <el-input v-model='form.otherSwampNum' placeholder='请输入其他沼泽地' />
                </el-form-item>
                <el-form-item label='沿海滩涂' prop='coastalMudflatNum'>
                    <el-input v-model='form.coastalMudflatNum' placeholder='请输入沿海滩涂' />
                </el-form-item>
                <el-form-item label='内陆滩涂' prop='inlandMudflatNum'>
                    <el-input v-model='form.inlandMudflatNum' placeholder='请输入内陆滩涂' />
                </el-form-item>
                <el-form-item label='红树林' prop='mangroveNum'>
                    <el-input v-model='form.mangroveNum' placeholder='请输入红树林' />
                </el-form-item>
                <el-form-item label='状态' prop='dataStatus'>
                    <el-input v-model='form.dataStatus' placeholder='请输入数据状态' />
                </el-form-item>
            </el-form>
            <template #footer>
                <div slot='footer' class='dialog-footer'>
                    <el-button type='primary' @click='submitForm'>确 定</el-button>
                    <el-button @click='cancel'>取 消</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script>
    import '@/views/sysforestresource/assets/styles/index.scss' // global css
    import { getDicts } from "@/api/sysforestresource/dict";
    import {
        listForWetlandAreaRpt,
        getForWetlandAreaRpt,
        delForWetlandAreaRpt,
        addForWetlandAreaRpt,
        updateForWetlandAreaRpt,
        delForWetlandAreaRpts,
        dataBack,
        dataSubmit
    } from '@/api/sysforestresource/forest/ForWetlandAreaRpt'
    import { queryOrgTreeByUserOrg } from '@/api/sysforestresource/login'
    import { selectDictLabel } from "@/api/sysforestresource/utils/cop";
    import exceldownload from '@/views/sysforestresource/components/exceldownload'
    import FileUpload1 from '@/views/sysforestresource/components/FileUpload1'

    export default {
        name: '/forest/ForWetlandAreaRpt/queryByPage',
        components: {
            exceldownload,
            FileUpload1,
        },
        data() {
            return {
                // 遮罩层
                loading: true,
                // 选中数组
                ids: [],
                // 非单个禁用
                single: true,
                // 非多个禁用
                multiple: true,
                // 显示搜索条件
                showSearch: true,
                // 总条数
                total: 0,
                // 各类湿地面积统计表表格数据
                ForWetlandAreaRptList: [],
                // 弹出层标题
                title: '',
                // 是否显示弹出层
                open: false,
                // 查询参数
                queryParams: {
                    page: 1,
                    rows: 10,
                    orgCode: null,
                    orgName: null,
                    statYear: null,
                    totalNum: null,
                    forSwampNum: null,
                    shrubSwampNum: null,
                    swampGrassNum: null,
                    otherSwampNum: null,
                    coastalMudflatNum: null,
                    inlandMudflatNum: null,
                    mangroveNum: null,
                    dataStatus: null,
                    statusCd: null,
                },
                // 表单参数
                form: {},
                // 表单校验
                rules: {},
                url:
                    import.meta.env.VITE_APP_BASE_API +
                    '/bdh-forest-resource-api/forest/ForWetlandAreaRpt/exportTemplate',
                uploadFileUrl:
                    import.meta.env.VITE_APP_BASE_API + '/bdh-forest-resource-api/forest/ForWetlandAreaRpt/importExcel',

                orgTreeList: [],
                dataStatusOptions: [],
                tableHeight: 200,
                searchHeight: 0,
            }
        },
        mounted() {
            this.searchHeight = this.$refs.searchDom.clientHeight;
            this.tableHeight = this.showSearch
                ? window.innerHeight - this.searchHeight - 220
                : window.innerHeight - 220
        },
        watch: {
            //搜索隐藏调整表格大小
            showSearch() {
                this.tableHeight = this.showSearch
                    ? window.innerHeight - this.searchHeight - 220
                    : window.innerHeight - 220;
            },
        },
        created() {
            this.getList()
            this.getOrgTreeByUserOrg()
            getDicts('for_sta_apply_status').then((response) => {
                this.dataStatusOptions = response.data
            })
        },
        methods: {
            dataStatusFormat(row, column) {
                return selectDictLabel(this.dataStatusOptions, row.dataStatus != null && row.dataStatus != undefined ? row.dataStatus.toString() : '')
            },

            async getOrgTreeByUserOrg() {
                let res = await queryOrgTreeByUserOrg()
                let { data, code } = res
                this.orgTreeList = data.children
            },

            farmHandleChangeTop(value) {
                var node = this.$refs.farmSelectTop.getCheckedNodes()[0]
                if (node != null) {
                    let data = node.data
                    this.queryParams.orgCode = data.orgNo
                } else {
                    this.queryParams.orgCode = null
                }

                if (this.$refs.farmSelectTop) {
                    this.$refs.farmSelectTop.togglePopperVisible() //监听值发生变化就关闭它
                }
            },
            /** 查询各类湿地面积统计表列表 */
            getList() {
                this.loading = true
                listForWetlandAreaRpt(this.queryParams).then(response => {
                    this.ForWetlandAreaRptList = response.data.records
                    this.total = response.data.total
                    this.loading = false
                })
            },
            // 取消按钮
            cancel() {
                this.open = false
                this.reset()
            },
            // 表单重置
            reset() {
                this.form = {
                    dataId: null,
                    orgCode: null,
                    orgName: null,
                    statYear: null,
                    totalNum: null,
                    forSwampNum: null,
                    shrubSwampNum: null,
                    swampGrassNum: null,
                    otherSwampNum: null,
                    coastalMudflatNum: null,
                    inlandMudflatNum: null,
                    mangroveNum: null,
                    remark: null,
                    createBy: null,
                    dataStatus: null,
                    createTime: null,
                    updateBy: null,
                    updateTime: null,
                    statusCd: null,
                }
                this.resetForm('formRef')
            },
            /** 搜索按钮操作 */
            handleQuery() {
                this.queryParams.page = 1
                this.getList()
            },
            /** 重置按钮操作 */
            resetQuery() {
                this.resetForm('queryForm')
                this.handleQuery()
            },
            // 多选框选中数据
            handleSelectionChange(selection) {
                this.ids = selection.map(item => item.dataId)
                this.single = selection.length !== 1
                this.multiple = !selection.length
            },
            /** 新增按钮操作 */
            handleAdd() {
                this.reset()
                this.open = true
                this.title = '添加各类湿地面积统计表'
            },
            /** 修改按钮操作 */
            handleUpdate(row) {
                this.reset()
                const dataId = row.dataId || this.ids
                getForWetlandAreaRpt(dataId).then(response => {
                    this.form = response.data
                    this.open = true
                    this.title = '修改各类湿地面积统计表'
                })
            },
            /** 提交按钮 */
            submitForm() {
                this.$refs['formRef'].validate(valid => {
                    if (valid) {
                        if (this.form.dataId != null) {
                            updateForWetlandAreaRpt(this.form).then(response => {
                                this.$message({
                                    message: '修改成功',
                                    type: 'success'
                                })
                                this.open = false
                                this.getList()
                            })
                        } else {
                            addForWetlandAreaRpt(this.form).then(response => {
                                this.$message({
                                    message: '新增成功',
                                    type: 'success'
                                })
                                this.open = false
                                this.getList()
                            })
                        }
                    }
                })
            },
            /** 删除按钮操作 */
            handleDelete(row) {
                const dataIds = row.dataId || this.ids
                this.$confirm('是否确认删除各类湿地面积统计表编号为"' + dataIds + '"的数据项?', '警告', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning',
                }).then(function () {
                    return Array.isArray(dataIds) ? delForWetlandAreaRpts(dataIds) : delForWetlandAreaRpt(dataIds)
                }).then(() => {
                    this.getList()
                    this.$message({
                        message: '删除成功',
                        type: 'success'
                    })
                })
            },
            handleDataSubmit(row) {
                const dataIds = row.dataId || this.ids
                this.$confirm('是否确认提交数据', '警告', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning',
                }).then(function () {
                    return dataSubmit({ dataId: dataIds })
                }).then(() => {
                    this.getList()
                    this.$message({
                        message: '提交成功',
                        type: 'success'
                    })
                })
            },
            handleDataBack(row) {
                const dataIds = row.dataId || this.ids
                this.$confirm('是否确认退回数据', '警告', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning',
                }).then(function () {
                    return dataBack({ dataId: dataIds })
                }).then(() => {
                    this.getList()
                    this.$message({
                        message: '退回成功',
                        type: 'success'
                    })
                })
            },
        },
    }


</script>
