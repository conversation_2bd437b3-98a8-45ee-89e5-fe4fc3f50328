<template>
    <div class='app-container'>
        <div ref="searchDom">
            <el-collapse-transition>
                <el-form :model='queryParams' class='queryClass form-line' ref='queryForm' :inline='true'
                    v-show='showSearch' label-width='68px'>
                    <el-row :gutter="20">
                        <el-col :span="6">
                            <el-form-item label='单位' prop='orgCode'>
                                <el-cascader ref='farmSelectTop' placeholder='请选择单位' v-model='queryParams.orgCode'
                                    @clear='queryParams.orgCode = null; queryParams.orgCode = null'
                                    :options='orgTreeList' :props="{
                                    value: 'orgNo',
                                    label: 'orgName',
                                    expandTrigger: 'hover',
                                    checkStrictly: true,
                                    emitPath: false,
                                }" size='medium' clearable @change='farmHandleChangeTop'>
                                </el-cascader>
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label='年度' prop='statYear'>
                                <el-date-picker v-model='queryParams.statYear' value-format='YYYY' type='year' clearable
                                    placeholder='请选择年度'>
                                </el-date-picker>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item>
                                <el-button type='primary' icon='Search' @click='handleQuery'>搜索</el-button>
                                <el-button icon='Refresh' @click='resetQuery'>重置</el-button>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
            </el-collapse-transition>
        </div>

        <el-row :gutter='10' class='mb8 plotClass'>
            <!-- <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"

        >新增</el-button>
      </el-col> -->
            <!-- <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"

        >修改</el-button>
      </el-col> -->
            <!-- <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"

        >删除</el-button>
      </el-col> -->
            <el-col :span='1.5' v-hasPermi="['ForWetlandChgRpt:exportTemplate']">
                <exceldownload ref='exceldownload' :param='queryParams' :url='url'
                    v-hasPermi="['ForWetlandChgRpt:exportTemplate']"></exceldownload>
            </el-col>
            <el-col :span='1.5' v-hasPermi="['ForWetlandChgRpt:importExcel']">
                <FileUpload1 :isShowTip='false' :default="['xlsx']" :text1="'导入'" :uploadFileUrl='uploadFileUrl'
                    @newlist='getList' v-hasPermi="['ForWetlandChgRpt:importExcel']"></FileUpload1>
            </el-col>
            <right-toolbar v-model:showSearch="showSearch" @queryTable='getList'></right-toolbar>
        </el-row>

        <el-table :height="tableHeight" :data='ForWetlandChgRptList' @selection-change='handleSelectionChange'
            :span-method='objectSpanMethod'>
            <el-table-column type='selection' width='55' align='center' />
            <el-table-column label='单位' align='center' prop='orgName' />
            <el-table-column label='年度' align='center' prop='statYear' />
            <el-table-column label='权属' align='center' prop='ownership' :formatter='ownershipFormat' />
            <el-table-column label='上一年实有面积(公顷)' align='center' prop='lastYearArea' />
            <el-table-column label='新增面积(公顷)' align='center' prop='newArea' />
            <el-table-column label='本年度减少面积' align='center'>
                <el-table-column label='合计' align='center' prop='lowerAreaTt' />
                <el-table-column label='经营活动面积' align='center'>
                    <el-table-column label='小计' align='center' prop='lowerAreaMaTt' />
                    <el-table-column label='征占用' align='center' prop='lowerAreaMaOc' />
                    <el-table-column label='其他' align='center' prop='lowerAreaMaOt' />
                </el-table-column>
                <el-table-column label='非经营活动面积' align='center'>
                    <el-table-column label='小计' align='center' prop='lowerAreaUnmaTt' />
                    <el-table-column label='过度开发' align='center' prop='lowerAreaUnmaOd' />
                    <el-table-column label='开垦' align='center' prop='lowerAreaUnmaRc' />
                    <el-table-column label='填埋' align='center' prop='lowerAreaUnmaLf' />
                    <el-table-column label='废水排放' align='center' prop='lowerAreaUnmaWd' />
                    <el-table-column label='其他' align='center' prop='lowerAreaUnmaOt' />
                </el-table-column>
            </el-table-column>
            <el-table-column label='本年度净增值(+-)' align='center' prop='addedValue' />
            <el-table-column label='本年度实有面积(公顷)' align='center' prop='thisYearArea' />
            <!-- <el-table-column label="备注" align="center" prop="remark" /> -->
            <el-table-column label='状态' align='center' prop='dataStatus' :formatter='dataStatusFormat' />
            <el-table-column label='操作' align='center' fixed="right" class-name='small-padding fixed-width'>
                <template #default="scope">
                    <!--          <el-button-->
                    <!--            icon="Edit"-->
                    <!--            @click="handleUpdate(scope.row)"-->
                    <!--          >修改</el-button>-->
                    <!--          <el-button-->
                    <!--            icon="Delete"-->
                    <!--            @click="handleDelete(scope.row)"-->
                    <!--          >删除</el-button>-->
                    <template v-if='scope.row.dataStatus == "3"'>
                        <el-button type='text' v-hasPermi="['ForWetlandChgRpt:submit']"
                            @click='handleDataSubmit(scope.row)'>提交
                        </el-button>
                    </template>
                    <template v-if='scope.row.dataStatus == "2"'>
                        <el-button type='text' v-hasPermi="['ForWetlandChgRpt:back']"
                            @click='handleDataBack(scope.row)'>退回
                        </el-button>
                    </template>
                </template>
            </el-table-column>
        </el-table>

        <pagination v-show='total>0' :total='total' v-model:page='queryParams.page' v-model:limit='queryParams.rows'
            @pagination='getList' />

        <!-- 添加或修改年度湿地变化表对话框 -->
        <el-dialog :title='title' v-model='open' width='500px' append-to-body>
            <el-form ref='formRef' :model='form' :rules='rules' label-width='80px'>
                <el-form-item label='组织机构编码' prop='orgCode'>
                    <el-input v-model='form.orgCode' placeholder='请输入组织机构编码' />
                </el-form-item>
                <el-form-item label='组织机构名称' prop='orgName'>
                    <el-input v-model='form.orgName' placeholder='请输入组织机构名称' />
                </el-form-item>
                <el-form-item label='年份' prop='statYear'>
                    <el-input v-model='form.statYear' placeholder='请输入年份' />
                </el-form-item>
                <el-form-item label='权属' prop='ownership'>
                    <el-input v-model='form.ownership' placeholder='请输入权属' />
                </el-form-item>
                <el-form-item label='上一年实有面积' prop='lastYearArea'>
                    <el-input v-model='form.lastYearArea' placeholder='请输入上一年实有面积' />
                </el-form-item>
                <el-form-item label='新增面积' prop='newArea'>
                    <el-input v-model='form.newArea' placeholder='请输入新增面积' />
                </el-form-item>
                <el-form-item label='本年度减少面积-合计' prop='lowerAreaTt'>
                    <el-input v-model='form.lowerAreaTt' placeholder='请输入本年度减少面积-合计' />
                </el-form-item>
                <el-form-item label='本年度减少面积-经营活动面积-小计' prop='lowerAreaMaTt'>
                    <el-input v-model='form.lowerAreaMaTt' placeholder='请输入本年度减少面积-经营活动面积-小计' />
                </el-form-item>
                <el-form-item label='本年度减少面积-经营活动面积-征占用' prop='lowerAreaMaOc'>
                    <el-input v-model='form.lowerAreaMaOc' placeholder='请输入本年度减少面积-经营活动面积-征占用' />
                </el-form-item>
                <el-form-item label='本年度减少面积-经营活动面积-其他' prop='lowerAreaMaOt'>
                    <el-input v-model='form.lowerAreaMaOt' placeholder='请输入本年度减少面积-经营活动面积-其他' />
                </el-form-item>
                <el-form-item label='本年度减少面积-非经营活动面积-小计' prop='lowerAreaUnmaTt'>
                    <el-input v-model='form.lowerAreaUnmaTt' placeholder='请输入本年度减少面积-非经营活动面积-小计' />
                </el-form-item>
                <el-form-item label='本年度减少面积-非经营活动面积-过度开发' prop='lowerAreaUnmaOd'>
                    <el-input v-model='form.lowerAreaUnmaOd' placeholder='请输入本年度减少面积-非经营活动面积-过度开发' />
                </el-form-item>
                <el-form-item label='本年度减少面积-非经营活动面积-开垦' prop='lowerAreaUnmaRc'>
                    <el-input v-model='form.lowerAreaUnmaRc' placeholder='请输入本年度减少面积-非经营活动面积-开垦' />
                </el-form-item>
                <el-form-item label='本年度减少面积-非经营活动面积-填埋' prop='lowerAreaUnmaLf'>
                    <el-input v-model='form.lowerAreaUnmaLf' placeholder='请输入本年度减少面积-非经营活动面积-填埋' />
                </el-form-item>
                <el-form-item label='本年度减少面积-非经营活动面积-肥水排放' prop='lowerAreaUnmaWd'>
                    <el-input v-model='form.lowerAreaUnmaWd' placeholder='请输入本年度减少面积-非经营活动面积-肥水排放' />
                </el-form-item>
                <el-form-item label='本年度减少面积-非经营活动面积-其他' prop='lowerAreaUnmaOt'>
                    <el-input v-model='form.lowerAreaUnmaOt' placeholder='请输入本年度减少面积-非经营活动面积-其他' />
                </el-form-item>
                <el-form-item label='本年度净增值' prop='addedValue'>
                    <el-input v-model='form.addedValue' placeholder='请输入本年度净增值' />
                </el-form-item>
                <el-form-item label='本年度实有面积' prop='thisYearArea'>
                    <el-input v-model='form.thisYearArea' placeholder='请输入本年度实有面积' />
                </el-form-item>
                <el-form-item label='备注' prop='remark'>
                    <el-input v-model='form.remark' placeholder='请输入备注' />
                </el-form-item>
                <el-form-item label='数据状态' prop='dataStatus'>
                    <el-input v-model='form.dataStatus' placeholder='请输入数据状态' />
                </el-form-item>
                <el-form-item label='生效状态 1：生效，2：失效' prop='statusCd'>
                    <el-input v-model='form.statusCd' placeholder='请输入生效状态 1：生效，2：失效' />
                </el-form-item>
            </el-form>
            <template #footer>
                <div slot='footer' class='dialog-footer'>
                    <el-button type='primary' @click='submitForm'>确 定</el-button>
                    <el-button @click='cancel'>取 消</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script>
    import '@/views/sysforestresource/assets/styles/index.scss' // global css
    import { getDicts } from "@/api/sysforestresource/dict";
    import {
        listForWetlandChgRpt,
        getForWetlandChgRpt,
        delForWetlandChgRpt,
        addForWetlandChgRpt,
        updateForWetlandChgRpt,
        delForWetlandChgRpts,
        dataBack,
        dataSubmit,
    } from '@/api/sysforestresource/forest/ForWetlandChgRpt'
    import { queryOrgTreeByUserOrg } from '@/api/sysforestresource/login'
    import { selectDictLabel } from "@/api/sysforestresource/utils/cop";
    import exceldownload from '@/views/sysforestresource/components/exceldownload'
    import FileUpload1 from '@/views/sysforestresource/components/FileUpload1'

    export default {
        name: '/forest/ForWetlandChgRpt/queryByPage',
        components: {
            exceldownload,
            FileUpload1,
        },
        data() {
            return {
                // 遮罩层
                loading: true,
                // 选中数组
                ids: [],
                // 非单个禁用
                single: true,
                // 非多个禁用
                multiple: true,
                // 显示搜索条件
                showSearch: true,
                // 总条数
                total: 0,
                // 年度湿地变化表表格数据
                ForWetlandChgRptList: [],
                // 弹出层标题
                title: '',
                // 是否显示弹出层
                open: false,
                // 查询参数
                queryParams: {
                    page: 1,
                    rows: 10,
                    orgCode: null,
                    orgName: null,
                    statYear: null,
                    ownership: null,
                    lastYearArea: null,
                    newArea: null,
                    lowerAreaTt: null,
                    lowerAreaMaTt: null,
                    lowerAreaMaOc: null,
                    lowerAreaMaOt: null,
                    lowerAreaUnmaTt: null,
                    lowerAreaUnmaOd: null,
                    lowerAreaUnmaRc: null,
                    lowerAreaUnmaLf: null,
                    lowerAreaUnmaWd: null,
                    lowerAreaUnmaOt: null,
                    addedValue: null,
                    thisYearArea: null,
                    dataStatus: null,
                    statusCd: null,
                },
                // 表单参数
                form: {},
                // 表单校验
                rules: {},
                url:
                    import.meta.env.VITE_APP_BASE_API +
                    '/bdh-forest-resource-api/forest/ForWetlandChgRpt/exportTemplate',
                uploadFileUrl:
                    import.meta.env.VITE_APP_BASE_API + '/bdh-forest-resource-api/forest/ForWetlandChgRpt/importExcel',

                orgTreeList: [],
                dataStatusOptions: [],
                ownershipOptions: [],
                tableHeight: 200,
                searchHeight: 0,
            }
        },
        created() {
            this.getList()
            this.getOrgTreeByUserOrg()
            getDicts('for_sta_apply_status').then((response) => {
                this.dataStatusOptions = response.data
            })
            getDicts('for_ownership_cate_rpt').then((response) => {
                this.ownershipOptions = response.data
            })
        },
        mounted() {
            this.searchHeight = this.$refs.searchDom.clientHeight;
            this.tableHeight = this.showSearch
                ? window.innerHeight - this.searchHeight - 220
                : window.innerHeight - 220
        },
        watch: {
            //搜索隐藏调整表格大小
            showSearch() {
                this.tableHeight = this.showSearch
                    ? window.innerHeight - this.searchHeight - 220
                    : window.innerHeight - 220;
            },
        },
        methods: {
            objectSpanMethod({ row, column, rowIndex, columnIndex }) {
                if (columnIndex === 1) {
                    const rows = this.ForWetlandChgRptList
                    const prevRow = rows[rowIndex - 1]
                    let nextRow = rows[rowIndex + 1]
                    if (prevRow && prevRow.orgCode === row.orgCode) {
                        return {
                            rowspan: 0,
                            colspan: 1,
                        }
                    } else {
                        let rowspan = 1
                        while (nextRow && nextRow.orgCode === row.orgCode) {
                            rowspan += 1
                            nextRow = rows[rowIndex + rowspan]
                        }
                        return {
                            rowspan,
                            colspan: 1,
                        }
                    }
                }
                if (columnIndex === 2) {
                    const rows = this.ForWetlandChgRptList
                    const prevRow = rows[rowIndex - 1]
                    let nextRow = rows[rowIndex + 1]
                    if (prevRow && prevRow.orgCode === row.orgCode && prevRow.statYear === row.statYear) {
                        return {
                            rowspan: 0,
                            colspan: 1,
                        }
                    } else {
                        let rowspan = 1
                        while (nextRow && nextRow.orgCode === row.orgCode && nextRow.statYear === row.statYear) {
                            rowspan += 1
                            nextRow = rows[rowIndex + rowspan]
                        }
                        return {
                            rowspan,
                            colspan: 1,
                        }
                    }
                }
            },

            ownershipFormat(row, column) {
                return selectDictLabel(this.ownershipOptions, row.ownership != null && row.ownership != undefined ? row.ownership.toString() : '')
            },
            dataStatusFormat(row, column) {
                return selectDictLabel(this.dataStatusOptions, row.dataStatus != null && row.dataStatus != undefined ? row.dataStatus.toString() : '')
            },


            async getOrgTreeByUserOrg() {
                let res = await queryOrgTreeByUserOrg()
                let { data, code } = res
                this.orgTreeList = data.children
            },

            farmHandleChangeTop(value) {
                var node = this.$refs.farmSelectTop.getCheckedNodes()[0]
                if (node != null) {
                    let data = node.data
                    this.queryParams.orgCode = data.orgNo
                } else {
                    this.queryParams.orgCode = null
                }

                if (this.$refs.farmSelectTop) {
                    this.$refs.farmSelectTop.togglePopperVisible() //监听值发生变化就关闭它
                }
            },

            /** 查询年度湿地变化表列表 */
            getList() {
                this.loading = true
                listForWetlandChgRpt(this.queryParams).then(response => {
                    this.ForWetlandChgRptList = response.data.records
                    this.total = response.data.total
                    this.loading = false
                })
            },
            // 取消按钮
            cancel() {
                this.open = false
                this.reset()
            },
            // 表单重置
            reset() {
                this.form = {
                    dataId: null,
                    orgCode: null,
                    orgName: null,
                    statYear: null,
                    ownership: null,
                    lastYearArea: null,
                    newArea: null,
                    lowerAreaTt: null,
                    lowerAreaMaTt: null,
                    lowerAreaMaOc: null,
                    lowerAreaMaOt: null,
                    lowerAreaUnmaTt: null,
                    lowerAreaUnmaOd: null,
                    lowerAreaUnmaRc: null,
                    lowerAreaUnmaLf: null,
                    lowerAreaUnmaWd: null,
                    lowerAreaUnmaOt: null,
                    addedValue: null,
                    thisYearArea: null,
                    remark: null,
                    dataStatus: null,
                    createBy: null,
                    createTime: null,
                    updateBy: null,
                    updateTime: null,
                    statusCd: null,
                }
                this.resetForm('formRef')
            },
            /** 搜索按钮操作 */
            handleQuery() {
                this.queryParams.page = 1
                this.getList()
            },
            /** 重置按钮操作 */
            resetQuery() {
                this.resetForm('queryForm')
                this.handleQuery()
            },
            // 多选框选中数据
            handleSelectionChange(selection) {
                this.ids = selection.map(item => item.dataId)
                this.single = selection.length !== 1
                this.multiple = !selection.length
            },
            /** 新增按钮操作 */
            handleAdd() {
                this.reset()
                this.open = true
                this.title = '添加年度湿地变化表'
            },
            /** 修改按钮操作 */
            handleUpdate(row) {
                this.reset()
                const dataId = row.dataId || this.ids
                getForWetlandChgRpt(dataId).then(response => {
                    this.form = response.data
                    this.open = true
                    this.title = '修改年度湿地变化表'
                })
            },
            /** 提交按钮 */
            submitForm() {
                this.$refs['formRef'].validate(valid => {
                    if (valid) {
                        if (this.form.dataId != null) {
                            updateForWetlandChgRpt(this.form).then(response => {
                                this.$message({
                                    message: '修改成功',
                                    type: 'success'
                                })
                                this.open = false
                                this.getList()
                            })
                        } else {
                            addForWetlandChgRpt(this.form).then(response => {
                                this.$message({
                                    message: '新增成功',
                                    type: 'success'
                                })
                                this.open = false
                                this.getList()
                            })
                        }
                    }
                })
            },
            /** 删除按钮操作 */
            handleDelete(row) {
                const dataIds = row.dataId || this.ids
                this.$confirm('是否确认删除年度湿地变化表编号为"' + dataIds + '"的数据项?', '警告', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning',
                }).then(function () {
                    return Array.isArray(dataIds) ? delForWetlandChgRpts(dataIds) : delForWetlandChgRpt(dataIds)
                }).then(() => {
                    this.getList()
                    this.$message({
                        message: '删除成功',
                        type: 'success'
                    })
                })
            },
            handleDataSubmit(row) {
                const dataIds = row.dataId || this.ids
                this.$confirm('是否确认提交数据', '警告', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning',
                }).then(function () {
                    return dataSubmit({ dataId: dataIds })
                }).then(() => {
                    this.getList()
                    this.$message({
                        message: '提交成功',
                        type: 'success'
                    })
                })
            },
            handleDataBack(row) {
                const dataIds = row.dataId || this.ids
                this.$confirm('是否确认退回数据', '警告', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning',
                }).then(function () {
                    return dataBack({ dataId: dataIds })
                }).then(() => {
                    this.getList()
                    this.$message({
                        message: '退回成功',
                        type: 'success'
                    })
                })
            },
        },
    }


</script>
