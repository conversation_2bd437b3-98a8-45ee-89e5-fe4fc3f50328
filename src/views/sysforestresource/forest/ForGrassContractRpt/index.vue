<template>
    <div class='app-container'>
        <div ref="searchDom">
            <el-collapse-transition>
                <el-form :model='queryParams' class='queryClass form-line' ref='queryForm' :inline='true'
                    v-show='showSearch' label-width='68px'>
                    <el-row :gutter="20">
                        <el-col :span="6">
                            <el-form-item label='单位' prop='orgCode'>
                                <el-cascader ref='farmSelectTop' style='width: 400px' placeholder='请选择单位'
                                    v-model='queryParams.orgCode'
                                    @clear='queryParams.orgCode = null; queryParams.orgCode = null'
                                    :options='orgTreeList' :props="{
                                    value: 'orgNo',
                                    label: 'orgName',
                                    expandTrigger: 'hover',
                                    checkStrictly: true,
                                    emitPath: false,
                                }" size='medium' clearable @change='farmHandleChangeTop'>
                                </el-cascader>
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label='年度' prop='statYear'>
                                <el-date-picker v-model='queryParams.statYear' value-format='YYYY' type='year' clearable
                                    placeholder='请选择年度'>
                                </el-date-picker>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item>
                                <el-button type='primary' icon='Search' @click='handleQuery'>搜索</el-button>
                                <el-button icon='Refresh' @click='resetQuery'>重置</el-button>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
            </el-collapse-transition>
        </div>

        <el-row :gutter='10' class='mb8 plotClass'>
            <!-- <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"

        >新增</el-button>
      </el-col> -->
            <!-- <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"

        >修改</el-button>
      </el-col> -->
            <!-- <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"

        >删除</el-button>
      </el-col> -->
            <el-col :span='1.5' v-hasPermi="['ForGrassContractRpt:exportTemplate']">
                <exceldownload ref='exceldownload' :param='queryParams' :url='url'
                    v-hasPermi="['ForGrassContractRpt:exportTemplate']"></exceldownload>
            </el-col>
            <el-col :span='1.5' v-hasPermi="['ForGrassContractRpt:importExcel']">
                <FileUpload1 :isShowTip='false' :default="['xlsx']" :text1="'导入'" :uploadFileUrl='uploadFileUrl'
                    @newlist='getList' v-hasPermi="['ForGrassContractRpt:importExcel']"></FileUpload1>
            </el-col>
            <right-toolbar v-model:showSearch="showSearch" @queryTable='getList'></right-toolbar>
        </el-row>

        <el-table :height="tableHeight" :data='ForGrassContractRptList' @selection-change='handleSelectionChange'>
            <el-table-column type='selection' width='55' align='center' />
            <el-table-column label='单位' align='center' prop='orgName' />
            <el-table-column label='年度' align='center' prop='statYear' />
            <el-table-column label='承包经营面积' align='center'>
                <el-table-column label='小计' align='center' prop='contractAreaTt' />
                <el-table-column label='国有' align='center' prop='contractAreaSo' />
                <el-table-column label='集体' align='center' prop='contractAreaCo' />
            </el-table-column>
            <el-table-column label='承包方式' align='center'>
                <el-table-column label='家庭承包' align='center' prop='contractTypeFa' />
                <el-table-column label='联户承包' align='center' prop='contractTypeJh' />
                <el-table-column label='竞价承包' align='center' prop='contractTypeBi' />
                <el-table-column label='其他方式' align='center' prop='contractTypeOt' />
            </el-table-column>
            <el-table-column label='承包年限' align='center'>
                <el-table-column label='5年以下' align='center' prop='contractDur5' />
                <el-table-column label='5-15年' align='center' prop='contractDur15' />
                <el-table-column label='16-30年' align='center' prop='contractDur30' />
                <el-table-column label='30年以上' align='center' prop='contractDurM30' />
            </el-table-column>
            <el-table-column label='承包合同数' align='center' prop='contractNum' />
            <!-- <el-table-column label="备注" align="center" prop="remark" /> -->
            <el-table-column label='状态' align='center' prop='dataStatus' :formatter='dataStatusFormat' />
            <el-table-column label='操作' align='center' fixed="right" class-name='small-padding fixed-width'>
                <template #default="scope">
                    <!--          <el-button-->
                    <!--            type="primary"-->
                    <!--            icon="Edit"-->
                    <!--            @click="handleUpdate(scope.row)"-->

                    <!--          >修改</el-button>-->
                    <!--          <el-button-->
                    <!--            type="primary"-->
                    <!--            icon="Delete"-->
                    <!--            @click="handleDelete(scope.row)"-->

                    <!--          >删除</el-button>-->
                    <template v-if='scope.row.dataStatus == "3"'>
                        <el-button link type='primary' v-hasPermi="['ForGrassContractRpt:submit']"
                            @click='handleDataSubmit(scope.row)'>提交
                        </el-button>
                    </template>
                    <template v-if='scope.row.dataStatus == "2"'>
                        <el-button link type='primary' v-hasPermi="['ForGrassContractRpt:back']"
                            @click='handleDataBack(scope.row)'>退回
                        </el-button>
                    </template>
                </template>
            </el-table-column>
        </el-table>

        <pagination v-show='total>0' :total='total' v-model:page='queryParams.page' v-model:limit='queryParams.rows'
            @pagination='getList' />

        <!-- 添加或修改草原承包经营情况统计表对话框 -->
        <el-dialog :title='title' v-model='open' width='500px' append-to-body>
            <el-form ref='formRef' :model='form' :rules='rules' label-width='80px'>
                <el-form-item label='组织机构编码' prop='orgCode'>
                    <el-input v-model='form.orgCode' placeholder='请输入组织机构编码' />
                </el-form-item>
                <el-form-item label='组织机构名称' prop='orgName'>
                    <el-input v-model='form.orgName' placeholder='请输入组织机构名称' />
                </el-form-item>
                <el-form-item label='年份' prop='statYear'>
                    <el-input v-model='form.statYear' placeholder='请输入年份' />
                </el-form-item>
                <el-form-item label='承包经营面积-小计' prop='contractAreaTt'>
                    <el-input v-model='form.contractAreaTt' placeholder='请输入承包经营面积-小计' />
                </el-form-item>
                <el-form-item label='承包经营面积-国有' prop='contractAreaSo'>
                    <el-input v-model='form.contractAreaSo' placeholder='请输入承包经营面积-国有' />
                </el-form-item>
                <el-form-item label='承包经营面积-集体' prop='contractAreaCo'>
                    <el-input v-model='form.contractAreaCo' placeholder='请输入承包经营面积-集体' />
                </el-form-item>
                <el-form-item label='承包方式-家庭承包' prop='contractTypeFa'>
                    <el-input v-model='form.contractTypeFa' placeholder='请输入承包方式-家庭承包' />
                </el-form-item>
                <el-form-item label='承包方式-联户承包' prop='contractTypeJh'>
                    <el-input v-model='form.contractTypeJh' placeholder='请输入承包方式-联户承包' />
                </el-form-item>
                <el-form-item label='承包方式-竞价承包' prop='contractTypeBi'>
                    <el-input v-model='form.contractTypeBi' placeholder='请输入承包方式-竞价承包' />
                </el-form-item>
                <el-form-item label='承包方式-其他方式' prop='contractTypeOt'>
                    <el-input v-model='form.contractTypeOt' placeholder='请输入承包方式-其他方式' />
                </el-form-item>
                <el-form-item label='承包年限-5年以下' prop='contractDur5'>
                    <el-input v-model='form.contractDur5' placeholder='请输入承包年限-5年以下' />
                </el-form-item>
                <el-form-item label='承包年限-5-15年' prop='contractDur15'>
                    <el-input v-model='form.contractDur15' placeholder='请输入承包年限-5-15年' />
                </el-form-item>
                <el-form-item label='承包年限-16-30年' prop='contractDur30'>
                    <el-input v-model='form.contractDur30' placeholder='请输入承包年限-16-30年' />
                </el-form-item>
                <el-form-item label='承包年限-30年以上' prop='contractDurM30'>
                    <el-input v-model='form.contractDurM30' placeholder='请输入承包年限-30年以上' />
                </el-form-item>
                <el-form-item label='承包合同数' prop='contractNum'>
                    <el-input v-model='form.contractNum' placeholder='请输入承包合同数' />
                </el-form-item>
                <el-form-item label='备注' prop='remark'>
                    <el-input v-model='form.remark' placeholder='请输入备注' />
                </el-form-item>
                <el-form-item label='数据状态' prop='dataStatus'>
                    <el-input v-model='form.dataStatus' placeholder='请输入数据状态' />
                </el-form-item>
                <el-form-item label='生效状态 1：生效，2：失效' prop='statusCd'>
                    <el-input v-model='form.statusCd' placeholder='请输入生效状态 1：生效，2：失效' />
                </el-form-item>
            </el-form>
            <template #footer>
                <div slot='footer' class='dialog-footer'>
                    <el-button type='primary' @click='submitForm'>确 定</el-button>
                    <el-button @click='cancel'>取 消</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script>
    import '@/views/sysforestresource/assets/styles/index.scss' // global css
    import { getDicts } from "@/api/sysforestresource/dict";
    import {
        listForGrassContractRpt,
        getForGrassContractRpt,
        delForGrassContractRpt,
        addForGrassContractRpt,
        updateForGrassContractRpt,
        delForGrassContractRpts,
        dataBack,
        dataSubmit,
    } from '@/api/sysforestresource/forest/ForGrassContractRpt'
    import { queryOrgTreeByUserOrg } from '@/api/sysforestresource/login'
    import { selectDictLabel } from "@/api/sysforestresource/utils/cop";
    import exceldownload from '@/views/sysforestresource/components/exceldownload'
    import FileUpload1 from '@/views/sysforestresource/components/FileUpload1'

    export default {
        name: '/forest/ForGrassContractRpt/queryByPage',
        components: {
            exceldownload,
            FileUpload1,
        },
        data() {
            return {
                // 遮罩层
                loading: true,
                // 选中数组
                ids: [],
                // 非单个禁用
                single: true,
                // 非多个禁用
                multiple: true,
                // 显示搜索条件
                showSearch: true,
                // 总条数
                total: 0,
                // 草原承包经营情况统计表表格数据
                ForGrassContractRptList: [],
                // 弹出层标题
                title: '',
                // 是否显示弹出层
                open: false,
                // 查询参数
                queryParams: {
                    page: 1,
                    rows: 10,
                    orgCode: null,
                    orgName: null,
                    statYear: null,
                    contractAreaTt: null,
                    contractAreaSo: null,
                    contractAreaCo: null,
                    contractTypeFa: null,
                    contractTypeJh: null,
                    contractTypeBi: null,
                    contractTypeOt: null,
                    contractDur5: null,
                    contractDur15: null,
                    contractDur30: null,
                    contractDurM30: null,
                    contractNum: null,
                    dataStatus: null,
                    statusCd: null,
                },
                // 表单参数
                form: {},
                // 表单校验
                rules: {},
                url:
                    import.meta.env.VITE_APP_BASE_API +
                    '/bdh-forest-resource-api/forest/ForGrassContractRpt/exportTemplate',
                uploadFileUrl:
                    import.meta.env.VITE_APP_BASE_API + '/bdh-forest-resource-api/forest/ForGrassContractRpt/importExcel',
                orgTreeList: [],
                dataStatusOptions: [],
                tableHeight: 200,
                searchHeight: 0,
            }
        },
        created() {
            this.getList()
            this.getOrgTreeByUserOrg()
            getDicts('for_sta_apply_status').then((response) => {
                this.dataStatusOptions = response.data
            })
        },
        mounted() {
            this.searchHeight = this.$refs.searchDom.clientHeight;
            this.tableHeight = this.showSearch
                ? window.innerHeight - this.searchHeight - 220
                : window.innerHeight - 220
        },
        watch: {
            //搜索隐藏调整表格大小
            showSearch() {
                this.tableHeight = this.showSearch
                    ? window.innerHeight - this.searchHeight - 220
                    : window.innerHeight - 220;
            },
        },
        methods: {
            dataStatusFormat(row, column) {
                return selectDictLabel(this.dataStatusOptions, row.dataStatus != null && row.dataStatus != undefined ? row.dataStatus.toString() : '')
            },


            async getOrgTreeByUserOrg() {
                let res = await queryOrgTreeByUserOrg()
                let { data, code } = res
                this.orgTreeList = data.children
            },

            farmHandleChangeTop(value) {
                var node = this.$refs.farmSelectTop.getCheckedNodes()[0]
                if (node != null) {
                    let data = node.data
                    this.queryParams.orgCode = data.orgNo
                } else {
                    this.queryParams.orgCode = null
                }

                if (this.$refs.farmSelectTop) {
                    this.$refs.farmSelectTop.togglePopperVisible() //监听值发生变化就关闭它
                }
            },

            /** 查询草原承包经营情况统计表列表 */
            getList() {
                this.loading = true
                listForGrassContractRpt(this.queryParams).then(response => {
                    this.ForGrassContractRptList = response.data.records
                    this.total = response.data.total
                    this.loading = false
                })
            },
            // 取消按钮
            cancel() {
                this.open = false
                this.reset()
            },
            // 表单重置
            reset() {
                this.form = {
                    dataId: null,
                    orgCode: null,
                    orgName: null,
                    statYear: null,
                    contractAreaTt: null,
                    contractAreaSo: null,
                    contractAreaCo: null,
                    contractTypeFa: null,
                    contractTypeJh: null,
                    contractTypeBi: null,
                    contractTypeOt: null,
                    contractDur5: null,
                    contractDur15: null,
                    contractDur30: null,
                    contractDurM30: null,
                    contractNum: null,
                    remark: null,
                    dataStatus: null,
                    createBy: null,
                    createTime: null,
                    updateBy: null,
                    updateTime: null,
                    statusCd: null,
                }
                this.resetForm('formRef')
            },
            /** 搜索按钮操作 */
            handleQuery() {
                this.queryParams.page = 1
                this.getList()
            },
            /** 重置按钮操作 */
            resetQuery() {
                this.resetForm('queryForm')
                this.handleQuery()
            },
            // 多选框选中数据
            handleSelectionChange(selection) {
                this.ids = selection.map(item => item.dataId)
                this.single = selection.length !== 1
                this.multiple = !selection.length
            },
            /** 新增按钮操作 */
            handleAdd() {
                this.reset()
                this.open = true
                this.title = '添加草原承包经营情况统计表'
            },
            /** 修改按钮操作 */
            handleUpdate(row) {
                this.reset()
                const dataId = row.dataId || this.ids
                getForGrassContractRpt(dataId).then(response => {
                    this.form = response.data
                    this.open = true
                    this.title = '修改草原承包经营情况统计表'
                })
            },
            /** 提交按钮 */
            submitForm() {
                this.$refs['formRef'].validate(valid => {
                    if (valid) {
                        if (this.form.dataId != null) {
                            updateForGrassContractRpt(this.form).then(response => {
                                this.$message({
                                    message: '修改成功',
                                    type: 'success'
                                })
                                this.open = false
                                this.getList()
                            })
                        } else {
                            addForGrassContractRpt(this.form).then(response => {
                                this.$message({
                                    message: '新增成功',
                                    type: 'success'
                                })
                                this.open = false
                                this.getList()
                            })
                        }
                    }
                })
            },
            /** 删除按钮操作 */
            handleDelete(row) {
                const dataIds = row.dataId || this.ids
                this.$confirm('是否确认删除草原承包经营情况统计表编号为"' + dataIds + '"的数据项?', '警告', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning',
                }).then(function () {
                    return Array.isArray(dataIds) ? delForGrassContractRpts(dataIds) : delForGrassContractRpt(dataIds)
                }).then(() => {
                    this.getList()
                    this.$message({
                        message: '删除成功',
                        type: 'success'
                    })
                })
            },
            handleDataSubmit(row) {
                const dataIds = row.dataId || this.ids
                this.$confirm('是否确认提交数据', '警告', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning',
                }).then(function () {
                    return dataSubmit({ dataId: dataIds })
                }).then(() => {
                    this.getList()
                    this.$message({
                        message: '提交成功',
                        type: 'success'
                    })
                })
            },
            handleDataBack(row) {
                const dataIds = row.dataId || this.ids
                this.$confirm('是否确认退回数据', '警告', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning',
                }).then(function () {
                    return dataBack({ dataId: dataIds })
                }).then(() => {
                    this.getList()
                    this.$message({
                        message: '退回成功',
                        type: 'success'
                    })
                })
            },
        },
    }


</script>
