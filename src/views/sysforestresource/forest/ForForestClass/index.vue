<template>
  <bdh-map-edit :mapHeight="screeHeight" :dataSetString="dataSetString" :dataSetId="dataSetId" :bolLoadForest="true"
    @updateGeom="updateGeom" ref="mapEdit">
    <div class="plotClass">
      <div ref="searchDom">
        <el-collapse-transition>
          <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="68px"
            class="topClass form-line">
            <el-row :gutter="20">
              <el-col :span="6">
                <el-form-item label="林场" prop="forestFarmId">
                  <el-select v-model="queryParams.forestFarmId" style="width: 120px;" placeholder="请选择林场">
                    <el-option v-for="farm in farmList" :key="farm.forestFarmId" :label="farm.forestFarmName"
                      :value="farm.forestFarmId" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="林班编号" prop="forestClassCode">
                  <el-input type="number" v-model="queryParams.forestClassCode" style="width: 120px;"  placeholder="请输入林班编号" clearable
                    @keyup.enter.native="handleQuery" />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="存在图形" prop="geom">
                  <el-select v-model="queryParams.geom" placeholder="请选择是否包含图形" style="width: 100px;" clearable @clear="queryParams.geom = null">
                    <el-option v-for="dict in hasGeomOptions" :key="dict.code" :label="dict.name" :value="dict.code" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item>
                  <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
                  <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-collapse-transition>
      </div>

      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button type="primary" icon="Plus" @click="handleAdd" v-hasPermi="['ResForestClass:insert']">新增
          </el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button icon="Edit" :disabled="single" @click="handleUpdate"
            v-hasPermi="['ResForestClass:update']">修改
          </el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button icon="Delete" :disabled="multiple" @click="handleDelete"
            v-hasPermi="['ResForestClass:logicDeleteByIds']">删除
          </el-button>
        </el-col>
        <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>

      <el-table :data="ForForestClassList" class="tableStyle" stripe :height="tableHeight" @row-click="rowClick"
        @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="林班编号" align="center" prop="forestClassCode" />
        <el-table-column label="林班名称" align="center" prop="forestClassName" />
        <el-table-column label="林场" align="center" prop="forForestFarmName" />
        <el-table-column label="林班面积(公顷)" align="center" prop="forestClassArea" />
        <!--<el-table-column label="描述" align="center" prop="forestClassDescribe"/>
          <el-table-column label="gis编码" align="center" prop="gisCode"/>
          <el-table-column label="位置信息" align="center" prop="geom"/>
          <el-table-column label="备注" align="center" prop="remark"/>
          <el-table-column label="数据状态" align="center" prop="dataStatus"/>
          <el-table-column label="生效状态 1：生效，2：失效" align="center" prop="statusCd"/>-->
        <el-table-column label="存在图形" align="center" prop="hasGeom" :formatter="dataFormat">
          <template #default="scope">
            <img v-if="scope.row.hasGeom" src="@/views/sysforestresource/forest/images/Y.png" alt="" width="24"
              height="24" />
            <img v-else src="@/views/sysforestresource/forest/images/N.png" alt="" width="24" height="24" />
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-button link type="primary" icon="FullScreen" @click.stop="handleDraw(scope.row)"
              v-hasPermi="['ResForestClass:updateForForestClassGeom']">绘制
            </el-button>
            <el-button link type="primary" icon="Edit" @click.stop="handleUpdate(scope.row)"
              v-hasPermi="['ResForestClass:update']">修改
            </el-button>
            <el-button link type="primary" icon="Delete" @click.stop="handleDelete(scope.row)"
              v-hasPermi="['ResForestClass:del']">删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.page" v-model:limit="queryParams.rows"
        @pagination="getList" />

      <!-- 添加或修改林班表对话框 -->
      <el-dialog draggable :title="title" v-model="open" width="660px" append-to-body class="addDialog">
        <el-form ref="formRef" :model="form" :rules="rules" label-width="140px" style="
            display: flex;
            flex-wrap: wrap;
            flex-direction: row;
            justify-content: flex-start;
          ">
          <el-form-item label="林场" prop="forestFarmId" class="el-form-itemgg">
            <el-select v-model="form.forestFarmId" placeholder="请输入林班名称">
              <el-option v-for="farm in farmList" :key="farm.forestFarmId" :label="farm.forestFarmName"
                :value="farm.forestFarmId" />
            </el-select>
          </el-form-item>
          <el-form-item label="林班编号" prop="forestClassCode" class="el-form-itemgg">
            <el-input type="number" v-model="form.forestClassCode" placeholder="请输入林班编号" />
          </el-form-item>
          <el-form-item label="林班名称" prop="forestClassName" class="el-form-itemgg">
            <el-input v-model="form.forestClassName" placeholder="请输入林班名称" />
          </el-form-item>
          <el-form-item label="林班面积（公顷）" prop="forestClassArea" class="el-form-itemgg">
            <el-input type="number" v-model="form.forestClassArea" placeholder="请输入林班面积" />
          </el-form-item>
          <el-form-item label="描述" prop="forestClassDescribe" class="el-form-itemgg" style="width: 555px">
            <el-input v-model="form.forestClassDescribe" type="textarea" placeholder="请输入描述"
              :autosize="{ minRows: 4, maxRows: 4 }" :style="{ width: '100%' }"></el-input>
          </el-form-item>
          <!--<el-form-item
              label="gis编码"
              prop="gisCode"
              class="el-form-itemgg"
              style="width: 555px"
            >
              <el-input
                v-model="form.gisCode"
                type="textarea"
                placeholder="请输入gis编码"
                :autosize="{ minRows: 4, maxRows: 4 }"
                :style="{ width: '100%' }"
              ></el-input>
            </el-form-item>-->
        </el-form>
        <template #footer>
          <div slot="footer" class="dialog-footer">
            <el-button type="primary" @click="submitForm">确 定</el-button>
            <el-button @click="cancel">取 消</el-button>
          </div>
        </template>
      </el-dialog>
      <!-- 添加或修改林班表对话框 -->
      <el-dialog draggable title="林班详情" v-model="openDetail" width="660px" append-to-body class="detailDialog" center>
        <el-form ref="formRef" :model="form" label-width="120px" style="
            display: flex;
            flex-wrap: wrap;
            flex-direction: row;
            justify-content: flex-start;
          ">
          <el-form-item label="林场" prop="forestFarmId" class="el-form-itemgg">
            <el-select disabled v-model="form.forestFarmId" placeholder="请输入林班名称">
              <el-option v-for="farm in farmList" :key="farm.forestFarmId" :label="farm.forestFarmName"
                :value="farm.forestFarmId" />
            </el-select>
          </el-form-item>
          <el-form-item label="林班编号" prop="forestClassCode" class="el-form-itemgg">
            <el-input type="number" v-model="form.forestClassCode" disabled />
          </el-form-item>
          <el-form-item label="林班名称" prop="forestClassName" class="el-form-itemgg">
            <el-input v-model="form.forestClassName" disabled />
          </el-form-item>
          <el-form-item label="林班面积（公顷）" prop="forestClassArea" class="el-form-itemgg">
            <el-input type="number" v-model="form.forestClassArea" disabled />
          </el-form-item>
          <el-form-item label="描述" prop="forestClassDescribe" class="el-form-itemgg" style="width: 555px">
            <div class="remark">{{form.forestClassDescribe}}</div>
          </el-form-item>
        </el-form>
      </el-dialog>
    </div>
  </bdh-map-edit>
</template>

<script>
  import {
    listForForestClass,
    getForForestClass,
    delForForestClass,
    addForForestClass,
    updateForForestClass,
    delForForestClasss,
    updateForForestClassGeom,
  } from "@/api/sysforestresource/forest/ForForestClass";

  import { listForForestFarmAll } from "@/api/sysforestresource/forest/ForForestFarm";

  // import * as myMapDrawer from "@/bdh-gis/mapbox-gl-draw";
  import { queryOrgTreeByUserOrg } from "@/api/sysforestresource/login";

  import BdhMapEdit from "@/views/sysforestresource/components/BdhMapEdit/index";
  import geomShow from "@/views/sysforestresource/mixins/geomShow";
  export default {
    name: "/forest/ForForestClass/queryByPage",
    mixins: [geomShow],
    components: {
      // myMapDrawer,
      BdhMapEdit,
    },

    data() {
      return {
        tableHeight: 0,
        searchHeight: 0,
        fullHeight: document.documentElement.clientHeight,
        switchValue: false,
        mapActiveIndex: 0,
        UploadUrl: import.meta.env.VITE_APP_BASE_API + "/bdh-forest-resource-api/resource/ResPlot/uploadPlot",

        screenWidth: `${document.body.clientWidth - 50}px`, // 屏幕宽
        screeHeight: `${document.body.clientHeight - 90}px`, // 屏幕高

        dataSetString: "bdh:for_forest_class",
        dataSetId: "forest_class_id",

        tableItemId: null,
        tableItemNo: null,

        viewOptions: {
          constrainResolution: true,
        },
        // 遮罩层
        loading: true,
        // 选中数组
        ids: [],
        names: [],
        // 非单个禁用
        single: true,
        // 非多个禁用
        multiple: true,
        // 显示搜索条件
        showSearch: true,
        // 总条数
        total: 0,
        // 林班表表格数据
        ForForestClassList: [],
        // 弹出层标题
        title: "",
        // 是否显示弹出层
        open: false,
        openDetail: false,
        // 查询参数
        queryParams: {
          page: 1,
          rows: 10,
          forestClassName: null,
          forestClassCode: null,
          forestFarmId: null,
          orgCode: null,
          forestClassArea: null,
          forestClassDescribe: null,
          gisCode: null,
          geom: null,
          dataStatus: null,
          statusCd: null,
        },
        hasGeomOptions: [
          { code: 1, id: 1, name: "是" },
          { code: 0, id: 0, name: "否" },
        ],
        // 表单参数
        form: {},
        // 表单校验
        rules: {
          forestFarmId: [
            { required: true, message: "林场不能为空", trigger: "blur" },
          ],
          forestClassCode: [
            { required: true, message: "林班编号不能为空", trigger: "blur" },
          ],
          forestClassName: [
            { required: true, message: "林班名称不能为空", trigger: "blur" },
          ],
          forestClassArea: [
            { required: true, message: "林班面积不能为空", trigger: "blur" },
          ],
        },
        farmList: [],
      };
    },
    watch:{
        //搜索隐藏调整表格大小
        showSearch() {
        this.tableHeight = this.showSearch
          ? window.innerHeight - this.searchHeight - 240
          : window.innerHeight - 240;
      },
    },
    mounted() {
      this.searchHeight = this.$refs.searchDom.clientHeight;
      this.tableHeight = this.showSearch
        ? window.innerHeight - this.searchHeight - 240
        : window.innerHeight - 240
    },
    methods: {
      /** 查询林班表列表 */
      getList() {
        this.loading = true;
        listForForestClass(this.queryParams).then((response) => {
          this.ForForestClassList = response.data.records;
          this.total = response.data.total;
          this.loading = false;
        });
      },
      getForestFarmsList() {
        listForForestFarmAll({ statusCd: 1 }).then((response) => {
          this.farmList = response.data;
        });
      },
      // 取消按钮
      cancel() {
        this.open = false;
        this.openDetail = false;
        this.reset();
      },
      // 表单重置
      reset() {
        this.form = {
          forestClassId: null,
          forestClassName: null,
          forestFarmId: null,
          orgCode: null,
          forestClassArea: null,
          forestClassDescribe: null,
          gisCode: null,
          geom: null,
          remark: null,
          dataStatus: null,
          createBy: null,
          createTime: null,
          updateBy: null,
          updateTime: null,
          statusCd: null,
        };
        this.resetForm("formRef");
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParams.page = 1;
        this.getList();
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.resetForm("queryForm");
        this.handleQuery();
      },
      // 多选框选中数据
      handleSelectionChange(selection) {
        this.ids = selection.map((item) => item.forestClassId);
        this.names = selection.map((item) => item.forestClassName);
        this.single = selection.length !== 1;
        this.multiple = !selection.length;
      },
      /** 新增按钮操作 */
      handleAdd() {
        this.reset();
        this.open = true;
        this.title = "添加林班";
      },
      /** 修改按钮操作 */
      handleUpdate(row) {
        this.reset();
        const forestClassId = row.forestClassId || this.ids;
        getForForestClass(forestClassId).then((response) => {
          this.form = response.data;
          this.form.geom = null;
          this.open = true;
          this.title = "修改林班";
        });
      },
      rowClick(row) {
        this.reset();
        const forestClassId = row.forestClassId || this.ids;
        getForForestClass(forestClassId).then((response) => {
          this.form = response.data;
          this.form.geom = null;
          this.openDetail = true;
        });
      },
      /** 提交按钮 */
      submitForm() {
        delete this.form.geom
        this.$refs["formRef"].validate((valid) => {
          if (valid) {
            if (this.form.forestClassId != null) {
              updateForForestClass(this.form).then((response) => {
                this.$message({
                  message: "修改成功",
                  type: 'success'
                });
                this.open = false;
                this.getList();
              });
            } else {
              addForForestClass(this.form).then((response) => {
                this.$message({
                  message: "新增成功",
                  type: 'success'
                })
                this.open = false;
                this.getList();
              });
            }
          }
        });
      },
      /** 删除按钮操作 */
      handleDelete(row) {
        const forestClassIds = row.forestClassId || this.ids;
        const forestClassNames = row.forestClassName || this.names;
        this.$confirm(
          '是否确认删除林班表林班名称为"' + forestClassNames + '"的数据项?',
          "提示",
          {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          }
        )
          .then(function () {
            return Array.isArray(forestClassIds)
              ? delForForestClasss(forestClassIds)
              : delForForestClass(forestClassIds);
          })
          .then(() => {
            this.getList();
            this.$message({
              message: "删除成功",
              type: 'success'
            });
          });
      },

      handleDraw(row) {
        //判断林班是否有图形，如果没有图形，尝试用下属小班定位范围。
        if (row.hasGeom == 0) {
          this.$refs.mapEdit.getExtentBySql(
            "bdh:for_sub_compartment",
            "forest_class_id=" + row.forestClassId
          );
        }

        let id = row.forestClassId;
        this.$refs.mapEdit.editStart(id);
      },

      //保存geom
      async updateGeom(id, geom) {
        let forestClassId = id;
        let res = await updateForForestClassGeom({ forestClassId, geom });
        let { data, code } = res;
        if (res.code === 0) {
          this.$message({
            message: "保存绘制成功",
            type: "success",
          });
          this.utils = false;
        } else {
          this.$message.error("保存绘制失败");
        }
        this.getList();
        this.$refs.mapEdit.clearAllandMove();
      },
      async getOrgTreeByUserOrg() {
        let res = await queryOrgTreeByUserOrg();
        let { data, code } = res;
        this.$refs.mapEdit.orgTreeLocation(res.data);
      },
    },

    created() {
      this.getList();
      this.getForestFarmsList();
      this.getOrgTreeByUserOrg();
    },
  };
</script>

<style lang="scss" scoped>
  .box-card {
    padding-bottom: 15px;
  }

  .tabsOne {
    padding: 10px;
  }

  .tabsOne1 {
    color: #d5e2e8;
    padding: 5px 0;
  }

  .tabsTitle {
    font-size: 17px;
    font-weight: 800;
    color: #69c1f1;
  }

  .pagination-container {
    border: none;
    background: transparent;
    margin: 0 !important;
    padding: 5px 0 !important;
    height: 40px;
  }

  .dropdownClass {
    z-index: 2;
    color: #ffffffbd;
    position: fixed;
    right: 10px;
    top: 55px;
    width: 122px;
    line-height: 40px;
    height: 40px;
    background: inherit;
    background-color: #0000006b;
    border: none;
    border-radius: 10px;
    -moz-box-shadow: none;
    -webkit-box-shadow: none;
  }

  .utileClass {
    z-index: 2;
    color: #ffffffbd;
    position: fixed;
    right: 140px;
    top: 95px;
    width: 216px;
    line-height: 40px;
    padding: 0 8px;
    height: 40px;
    background: inherit;
    background-color: #0000006b;
    border: none;
    border-radius: 10px;
    -moz-box-shadow: none;
    -webkit-box-shadow: none;
  }

  .handleIcon {
    padding: 5px 8px;
    color: #f5f1bb;
    font-weight: 600;
    //border-radius: 4px;
    //background: #1ab39447;
    //border: 1px solid #1ab394;
  }

  .el-form-itemgg {
    margin-bottom: 22px;
    width: 270px;
    margin-left: 15px;
  }

  .el-table__body tr:hover>td {
    background-color: transparent !important;
  }

  .el-table__row tr:hover>td {
    background-color: transparent !important;
  }

  .el-table__body tr.current-row>td {
    background-color: transparent !important;
  }
</style>
<style scoped>
  .leftSwitch :deep(.el-switch__core::before) {
    content: "标注";
    position: absolute;
    font-size: 12px;
    left: 22px;
    color: #ffffff;
  }

  .leftSwitch .is-checked :deep(.el-switch__core::before) {
    content: "标注";
    position: absolute;
    font-size: 12px;
    left: 6px;
    color: #fff;
  }

  .leftSwitch :deep(.el-switch__core:after) {
    top: 1px;
    width: 21px;
    height: 21px;
  }

  .leftSwitch :deep(.is-checked .el-switch__core) {
    border-color: #0000006b;
    background-color: #0000006b !important;
  }

  .leftSwitch :deep(.el-switch.is-checked .el-switch__core::after) {
    margin-left: -21px;
  }

  .leftSwitch :deep(.el-switch__core) {
    width: 54px !important;
    height: 26px;
    line-height: 24px;
    border-radius: 13px;
    background: #516349 !important;
  }

  :deep(.el-table__body-wrapper::-webkit-scrollbar-thumb) {
    background-color: #ddd;
    border-radius: 3px;
  }
  .box-card :deep(.el-card) {
    border: 1px solid #e6ebf521 !important;
    background-color: #ffffff0a !important;
  }

  .box-card :deep(.el-progress__text) {
    color: #d5e2e8;
  }
</style>

<style lang="scss">
  .el-popover.darkPopover {
    background-color: rgba(19, 54, 93, 0.6);
    border: #13365ddb;

    .popper__arrow,
    .popper__arrow::after {
      display: none !important;
    }
  }
</style>