<template>
  <div class="app-container">
    <div ref="searchDom">
      <el-collapse-transition>
        <el-form :model="queryParams" class="queryClass form-line" ref="queryForm" :inline="true" v-show="showSearch"
          label-width="68px">
          <el-row :gutter="20">
            <el-col :span="6">
              <el-form-item label="选中农场" prop="blStationNo" class="formitemgg">
                <el-cascader ref="farmSelectTop" :placeholder="slelectValTop" v-model="queryParams.blFarmNo" clearable
                  @clear="queryParams.blFarmNo = null" :options="orgTreeList"
                  :props="{ value:'orgNo',label:'orgName', expandTrigger: 'hover'}"
                  @change="farmHandleChangeTop"></el-cascader>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="属性名称" prop="columnName">
                <el-input v-model="queryParams.columnDesc" placeholder="请输入属性名称" clearable />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="属性类型" prop="groupNo">
                <el-select v-model="queryParams.groupNo" placeholder="请选择属性类型" clearable>
                  <el-option v-for="dict in plotTypeOptions" :key="dict.code" :label="dict.name" :value="dict.code" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="启用状态" prop="ifdisplay">
                <el-select v-model="queryParams.ifdisplay" placeholder="请选择启用状态" clearable>
                  <el-option v-for="dict in plotDisplayOptions" :key="dict.code" :label="dict.name"
                    :value="dict.code" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-collapse-transition>
    </div>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table ref="dragTable" :data="configList" :height="tableHeight" @selection-change="handleSelectionChange">
      <!--      <el-table-column type="selection" width="55" align="center" />-->
      <el-table-column label="属性类型" align="center" prop="groupNo" :formatter="groupNoFormat" />
      <el-table-column label="属性名称" align="center" prop="columnDesc" />
      <el-table-column label="启用状态" align="center" prop="ifdisplay" :formatter="ifdisplayFormat" />
      <el-table-column label="开始时间" align="center" prop="createTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="备注" align="center" prop="remark" />
      <el-table-column label="调整顺序" align="center">
        <template #default="scope">
          <el-button type="primary" @click="handleUp(scope.row.settingId)" v-hasPermi="['PlotConfig:MoveUp']">向上
          </el-button>
          <el-button type="primary" @click="handleDown(scope.row.settingId)" v-hasPermi="['PlotConfig:MoveDown']">向下
          </el-button>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope" class="leftSwitch" v-hasPermi="['PlotConfig:notDisPlay','PlotConfig:isDisPlay']">
          <el-switch style="display: block" :width="50" v-model="scope.row.ifdisplay" active-color="#13ce66"
            @change="openChange(scope.row)" :active-value="activeValue" :inactive-value="inactiveValue">
          </el-switch>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total>0" :total="total" v-model:page="queryParams.page" v-model:limit="queryParams.rows"
      @pagination="getList" />

  </div>
</template>

<script>
  import '@/views/syslandresource/assets/styles/index.scss' // global css
  import { isDisPlay, notDisPlay, queryByPage, MoveDown, MoveUp } from '@/api/syslandresource/ststem/flotConfig'
  import { queryOrgFarmTreeByUserOrg } from '@/api/syslandresource/login'
  import Sortable from 'sortablejs'
  import { checkPermi, checkRole } from '@/api/syslandresource/utils/permission'
  import { getDicts } from '@/api/syslandresource/utils/dict'
  import { selectDictLabel } from "@/api/syslandresource/utils/cop";
  export default {
    name: '/land/ResPlotDisplaySetting/queryPlotByPage',
    components: {
      checkPermi,
      checkRole
    },
    data() {
      return {
        oldTableList: [],
        newTableList: [],
        sortable: null,
        activeValue: 1,
        inactiveValue: 2,
        tableHeight: 200,
        // 遮罩层
        loading: true,
        // 选中数组
        ids: [],
        // 非单个禁用
        single: true,
        // 非多个禁用
        multiple: true,
        // 显示搜索条件
        showSearch: true,
        // 总条数
        total: 0,
        //农场默认选中
        slelectValTop: '请选择',
        orgTreeList: [],
        // 审核任务编辑表格数据
        configList: [],
        // 土地属性分组
        plotTypeOptions: [],
        //农场列表分组
        blFarmNoList: [],
        // 是否显示分钟
        plotDisplayOptions: [],
        // 查询参数
        queryParams: {
          page: 1,
          rows: 10,
          columnDesc: null,
          groupNo: null,
          ifdisplay: null,
          blFarmNo: null
        },
        searchHeight: 0,
      }
    },
    mounted() {
      this.searchHeight = this.$refs.searchDom.clientHeight;
      this.tableHeight = this.showSearch
        ? window.innerHeight - this.searchHeight - 220
        : window.innerHeight - 220
    },
    watch: {
      //搜索隐藏调整表格大小
      showSearch() {
        this.tableHeight = this.showSearch
          ? window.innerHeight - this.searchHeight - 220
          : window.innerHeight - 220;
      },
    },
    created() {
      this.getOrgTreeByUserOrg()
      this.getList()
      getDicts('res_plot_display_group').then(response => {
        this.plotTypeOptions = response.data
      })
      getDicts('ifdisplay').then(response => {
        this.plotDisplayOptions = response.data
      })
    },
    methods: {
      // 向上
      async handleUp(settingId) {
        let res = await MoveUp({ settingId })
        this.getList()
      },
      async handleDown(settingId) {
        let res = await MoveDown({ settingId })
        this.getList()
      },
      async openChange(val) {
        let _this = this
        if (val.ifdisplay === 2) {
          let res = await isDisPlay({ settingId: val.settingId })
          if (res.code === 0) {
            _this.$message({
              message: '修改成功',
              type: 'success'
            })
          } else {
            _this.$message.error(res.success)
            val.ifdisplay = val.ifdisplay === 1 ? 2 : 1
          }
          // this.getList();
        } else {
          let res = await notDisPlay({ settingId: val.settingId })
          if (res.code === 0) {
            _this.$message({
              message: '修改成功',
              type: 'success'
            })
          } else {
            _this.$message.error(res.success)
            val.ifdisplay = val.ifdisplay === 1 ? 2 : 1
          }
          // this.getList();
        }
      },
      /*查询农场组织机构*/
      async getOrgTreeByUserOrg() {
        let res = await queryOrgFarmTreeByUserOrg()
        let { data, code } = res
        this.orgTreeList = data.children
      },
      farmHandleChangeTop(value) {
        if (this.$refs.farmSelectTop) {
            this.$refs.farmSelectTop.togglePopperVisible() //监听值发生变化就关闭它
        }
      },
      /** 查询列表 */
      getList() {
        this.loading = true
        const no = this.queryParams.blFarmNo
        if (this.queryParams.blFarmNo !== null && this.queryParams.blFarmNo && typeof this.queryParams.blFarmNo !== 'string') {
          this.queryParams.blFarmNo = this.queryParams.blFarmNo[this.queryParams.blFarmNo.length - 1]
        }
        queryByPage(this.queryParams).then(response => {
          this.configList = response.data.records
          this.total = response.data.total
          this.loading = false
          this.queryParams.blFarmNo = no
          this.oldList = this.configList.map(v => v.settingId)
          this.newList = this.oldList.slice()
          this.$nextTick(() => {
            // this.setSort()
          })
        })
      },
      // 拖砖
      setSort() {
        const el = this.$refs.dragTable.$el.querySelectorAll('.el-table__body-wrapper > table > tbody')[0]
        this.sortable = Sortable.create(el, {
          ghostClass: 'sortable-ghost', // Class name for the drop placeholder,
          setData: function (dataTransfer) {
            // to avoid Firefox bug
            // Detail see : https://github.com/RubaXa/Sortable/issues/1012
            dataTransfer.setData('Text', '')
          },
          onEnd: evt => {
            // const targetRow = this.configList.splice(evt.oldIndex, 1)[0]
            // this.configList.splice(evt.newIndex, 0, targetRow)
            //
            // // for show the changes, you can delete in you code
            // const tempIndex = this.newList.splice(evt.oldIndex, 1)[0]
            // this.newList.splice(evt.newIndex, 0, tempIndex)
          }
        })
      },
      setSort1() {
        const el = this.$refs.dragTable.$el.querySelectorAll('.el-table__body-wrapper > table > tbody')[0]
        this.sortable = Sortable.create(el, {
          ghostClass: 'sortable-ghost', // Class name for the drop placeholder,
          setData: function (dataTransfer) {
            // to avoid Firefox bug
            // Detail see : https://github.com/RubaXa/Sortable/issues/1012
            dataTransfer.setData('Text', '')
          },
          onEnd: evt => {
            const targetRow = this.configList[evt.newIndex]
            this.$nextTick(() => {
              this.configList.fill(targetRow, 2, 5)
              // for show the changes, you can delete in you code
              const tempIndex = this.newList.splice(evt.oldIndex, 1)[0]
              this.newList.splice(evt.newIndex, 0, tempIndex)
            })
          }
        })
      },
      // 状态字典
      groupNoFormat(row, column) {
        return selectDictLabel(this.plotTypeOptions, row.groupNo.toString())
      },
      ifdisplayFormat(row, column) {
        return selectDictLabel(this.plotDisplayOptions, row.ifdisplay.toString())
      },
      // 取消按钮
      cancel() {
        this.open = false
      },
      // 表单重置
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParams.page = 1
        this.getList()
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.resetForm('queryForm')
        this.handleQuery()
      },
      // 多选框选中数据
      handleSelectionChange(selection) {
        this.ids = selection.map(item => item.taskId)
        this.single = selection.length !== 1
        this.multiple = !selection.length
      }

    }
  }


</script>
<style scoped>

  .el-switch {
    height: 24px;
  }

  .el-table__row .is-checked :deep(.el-switch__core::before) {
    content: "启用";
    position: absolute;
    font-size: 12px;
    left: 6px;
    color: #fff;
  }

  .el-table__row :deep(.el-switch__core:after) {
    top: 1px;
    width: 21px;
    height: 21px;
  }

  .el-table__row :deep(.el-switch.is-checked .el-switch__core::after) {
    margin-left: -21px;
  }

  .el-table__row :deep(.el-switch__core) {
    width: 40px !important;
    height: 24px;
    line-height: 24px;
    border-radius: 13px;
  }
</style>
