import { debounce } from "lodash";
export default {
    beforeMount(el, binding) {
        console.log(el, 3);
        // 使用 querySelectorAll 选择所有匹配的元素
        const selectDoms = document.querySelectorAll('.single-select-loadmore .el-select-dropdown__wrap, .single-select-loadmore2 .el-select-dropdown__wrap');
        
        selectDoms.forEach(selectDom => {
            function loadMores() {
                // 判断是否到底
                const isBase = this.scrollHeight - this.scrollTop <= (this.clientHeight + 10);
                if (isBase) {
                    binding.value && binding.value();
                }
            }
            selectDom.selectLoadMore = loadMores;
            selectDom.addEventListener('scroll', debounce(loadMores.bind(selectDom),300));
        });
 
        // 将 selectDoms 存储在 el 上以便在卸载时移除事件监听器
        el.selectDoms = selectDoms;
    },
    // 实例销毁
    beforeUnmount(el) {
        if (el.selectDoms) {
            el.selectDoms.forEach(selectDom => {
                if (selectDom.selectLoadMore) {
                    selectDom.removeEventListener('scroll', selectDom.selectLoadMore);
                    delete selectDom.selectLoadMore;
                }
            });
            delete el.selectDoms;
        }
    }
};