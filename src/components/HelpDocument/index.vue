<template>
  <div style="justify-content: center;align-items: center;display: flex;" @click="goto" >
      <svg-icon class-name="help-document-svg" icon-class="帮助文档1" />
      <span class="svg-text-span">帮助文档</span>

  </div>
</template>

<script setup>
import {useRouter} from "vue-router";

const {proxy}  = getCurrentInstance();
const url = ref('http://localhost:8090/help-doc');
const router = useRouter() // 路由

function goto() {
    window.open('/help-doc','_blank')
  // proxy.$tab.openPage("/bdh-question-feedback/work/help/list");
  // router.push({
  //   name: 'HelpDoc',
  // })
}
</script>

<style lang='scss' scoped>
.help-document-svg {
  display: inline-block;
  cursor: pointer;
  fill: #5a5e66;
  font-size: 18px;
  margin-right: 3px;
}
.svg-text-span{
  margin-left: 3px;
  font-size: 14px;
  color: var(--el-text-color-regular);
  font-weight: bold;
}
</style>
