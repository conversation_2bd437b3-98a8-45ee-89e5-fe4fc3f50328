<template>
  <div style="justify-content: center;align-items: center;display: flex;" @click="goto">
    <el-badge :value="messageValue" class="item help-el-badge">
       <svg-icon class-name="question-svg" icon-class="消息2" />
    </el-badge>
    <span class="svg-text-span">消息</span>
<!--        <NotificationHandler uniqueId="message"  @onNotification="onNotification"  :notification-types="['message']"/>-->
  </div>
</template>

<script setup>
import {useRouter} from "vue-router";
import NotificationHandler from '@/components/NotificationHandler'
import { ElNotification } from 'element-plus'
import {
    queryUnreadNew,
    getMqttInfo
} from '@/api/bdh-info-publish/messageSystem/message.js'

import userStore from '@/store/modules/user'
import * as mqtt from 'mqtt/dist/mqtt.min'
const router = useRouter() // 路由

// const url = ref('https://yiyan.baidu.com/');
const messageValue = ref(0)
const clientIdRandom = ref(Math.random() + '-' + new Date().getTime())
const client = ref({})

onMounted(async () => {
  try {
      getQueryUnread()
      connectfun()
  } catch (error) {
    console.error('获取消息数量失败:', error);
  }
});

//查铃铛旁边的数
function getQueryUnread() {
    queryUnreadNew({}).then((response) => {
        if (response.success) {
            messageValue.value = response.data
        } else {
            messageValue.value = 0
        }
    })
}

//连接并订阅
function connectfun() {

    getMqttInfo().then(response => {
        let options = {
            username: response.data.userName,
            password: response.data.passWord,
            topic: response.data.topic,
            cleanSession: false,
            keepAlive: 60,
            //clientId: process.env.VUE_APP_SYSTEM_CODE + '-' + userStore().staffId + '-' + clientIdRandom.value,
            clientId: '11-' + userStore().staffId + '-' + clientIdRandom.value,
            connectTimeout: 4000,
            reconnectPeriod: 200000 // 重连间隔
        }
        if (response.code == 0) {
            console.log('获取mqtt信息成功:')
            client.value = mqtt.connect(`ws://${response.data.ip}:${response.data.port}/mqtt`, options)
            //监听获取信息
            mqttMSG(response.data.topic)
        }
    })
}



function mqttMSG(topic) {
    // if (this.client.connected) {
    //   this.client.end();
    // }
    // mqtt连接
    client.value.on('connect', e => {
        console.log('连接成功:')
    })
    client.value.subscribe(topic)
    // 接收消息处理

    client.value.on('message', (topic, message) => {
        //const userId = process.env.VUE_APP_SYSTEM_CODE + '-' + userStore().staffId
        const userId = '-' + userStore().staffId
        const parsedMessage = JSON.parse(message.toString())

        if (parsedMessage.userId === userId) {
            getQueryUnread()
            ElNotification({
                title: '',
                dangerouslyUseHTMLString: true,
                duration: 5000,
                message: `<span>通知 ${parsedMessage.sendMessage.noticeName}</span><span style="color: #0c60e1; margin-left: 20px;">查看详情</span>`,
                onClick() {
                    //dialogOpenfun(parsedMessage.sendMessage.noticeId)

                    router.push({
                        name: 'notice',
                    })
                }
            })
        }
    })
    // 断开发起重连
    client.value.on('reconnect', error => {
        client.value.end()
        client.value = null
        connectfun()
        console.log('正在重连:', error)
    })
    // 链接异常处理
    client.value.on('error', error => {
        console.log('连接失败:', error)
    })
}


function goto() {
  router.push({
    name: 'notice',
  })
}
</script>

<style lang='scss' scoped>
.question-svg {
  display: inline-block;
  cursor: pointer;
  fill: #5a5e66;
  font-size: 18px;
  margin-right: 3px;
}
.svg-text-span{
  margin-left: 3px;
  font-size: 14px;
  color: var(--el-text-color-regular);
  font-weight: bold;
}
.help-el-badge {
  --el-badge-radius: 10px;
  --el-badge-font-size: 10px;
  --el-badge-padding: 3px;
  --el-badge-size: 16px;
  line-height: 20px;
}
</style>
