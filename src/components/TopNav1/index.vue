<template>
    <div style="display: flex;align-items: center;">
      <el-button @click="topNavChange" :type="settingsStore.topNav ? 'default' : 'primary'" plain circle  style="margin-right: 20px; margin-top: -8px;width: 35px; height: 35px; ">
        <img :src="settingsStore.topNav ? '/static/icon/singleSysMode.png' : '/static/icon/allSysMode.png'" style="scale: 120%;"  alt=""/>
      </el-button>
      <el-menu
            :default-active="activeMenu"
            mode="horizontal"
            :ellipsis="false"
            class="enhanced-nav"
            :close-on-click-outside="false"
            menu-trigger="click"
        >
            <!--    :close-on-click-outside="false"-->
            <!--    menu-trigger="click"-->
            <!-- 主入口 -->
            <el-sub-menu
                ref="subMenuRef"
                index="modules"
                popper-class="modules-popper"
                :style="{ '--theme': theme }"
                @mouseenter="handleMouseEnter"
                popper-append-to-body
            >
                <template #title>
        <span class="main-entry">
          <img
              v-if="activeModule?.meta?.navigationIcon"
              :src="getImageUrl(activeModule.meta.navigationIcon)"

              class="main-icon"
              alt="Main Icon"
          />
          <svg-icon v-else icon-class="menu" class="main-icon" />
          {{ activeModule?.meta?.title || groupedMenus[0]?.category || '功能模块' }}
        </span>
                </template>

                <div class="module-container"
                     @mouseenter="handleMouseEnter"
                     @mouseleave="handleMouseLeave">
                    <div class="category-columns">
                        <div
                            v-for="(group, index) in groupedMenus"
                            :key="index"
                            class="category-column"
                        >
                            <div class="category-title">{{ getLabelByValue(group.category) }}</div>
                            <div class="module-grid">
                                <div
                                    v-for="(item, i) in group.items"
                                    :key="i"
                                    class="module-card"
                                    :class="{ 'active-module': activeMenu === item.path }"
                                    @click="handleModuleSelect(item.path,true)"
                                >
                                    <div class="card-content">
                                        <div class="icon-text-container">
                                            <img
                                                v-if="item.meta?.navigationIcon"
                                                :src="getImageUrl(item.meta.navigationIcon)"
                                                class="module-icon"        style="height: 40px; width: 40px;"
                                                alt="Icon"
                                            />
                                            <svg-icon v-else icon-class="menu" class="module-icon"/>
                                            <div class="category-text">
                                                <span class="module-title">{{ item.meta.title }}</span>
                                                <span class="module-des">{{ item.remark }}</span>
                                            </div>
<!--                                            <svg-icon icon-class="table" color="#409EFF" @click.stop="handleModuleOpen(item)"/>-->
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </el-sub-menu>
        </el-menu>
    </div>
</template>

<script setup>
import { computed, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { constantRoutes } from "@/router"
import { isHttp } from '@/utils/validate'
import useAppStore from '@/store/modules/app'
import useSettingsStore from '@/store/modules/settings'
import usePermissionStore from '@/store/modules/permission'
import {getDictsForSso} from '@/api/menu'

const { proxy } = getCurrentInstance();

// 当前激活模块
const sys_menu_resource_type = ref([])
getDictsForSso("system_aggregation_type").then(resp => {
    sys_menu_resource_type.value = resp.data.map(p => ({
        label: p.name,
        value: p.code,
        elTagType: p.listClass,
        elTagClass: p.cssClass
    }))
})

const appStore = useAppStore()
const settingsStore = useSettingsStore()
const permissionStore = usePermissionStore()
const route = useRoute()
const router = useRouter()

// 主题颜色
const theme = computed(() => settingsStore.theme)

// 所有的路由信息
const routers = computed(() => permissionStore.topbarRouters)

const env = import.meta.env
const systemCodes = env.VITE_SYSTEM_CODE?.replace(/\n/g, '').split(',')


// 路径标准化处理
const normalizePath = (path) => {
    return path?.replace(/\/+/g, '/').replace(/\/$/, '').toLowerCase() || ''
}
// 辅助方法：通过 value 获取 label
const getLabelByValue = (value) => {
    return sys_menu_resource_type.value.find(item => item.value === value)?.label || '未分类'
}
// 顶部显示菜单（按分类分组）
const groupedMenus = computed(() => {
    const groups = new Map()

    routers.value.forEach(menu => {
        if (menu.hidden) return

        const categoryCode = menu.menuResourceType || '未分类'
        //console.error(sys_menu_resource_type)
        // const categoryName = sys_menu_resource_type.find(item => item.value === categoryCode)?.label || '未分类'
        const categoryName = categoryCode
        const targetMenu = menu.path === "/" ? menu.children[0] : menu

        if (!groups.has(categoryName)) {
            groups.set(categoryName, {
                category: categoryName,
                items: []
            })
        }
        groups.get(categoryName).items.push({
            ...targetMenu,
            path: normalizePath(targetMenu.path)
        })
    })

    return Array.from(groups.values())
})
console.log(2222,'routers----',routers.value)

// 当前激活模块
const activeModule = computed(() => {
    let activeModuleArr = groupedMenus.value
        .flatMap(g => g.items)
        .find(menu => normalizePath(menu.path) === normalizePath(activeMenu.value))
    appStore.setActiveModule(activeModuleArr?.meta?.title)
    if(!!activeModuleArr){
        appStore.setActiveModuleMenu(activeModuleArr)
    }
    return activeModuleArr
})

// 激活菜单处理（优化路径匹配）
const activeMenu = computed(() => {
    const currentPath = normalizePath(route.path)
    const hideList = ['/index', '/user/profile'].map(normalizePath)

    // 精确匹配优先
    const exactMatch = groupedMenus.value
        .flatMap(g => g.items)
        .find(menu => normalizePath(menu.path) === currentPath)

    if (exactMatch) {
        appStore.toggleSideBarHide(false)
        return exactMatch.path
    }

    // 路径回溯匹配
    const findBestMatch = () => {
        const segments = currentPath.split('/').filter(Boolean)
        for (let i = segments.length; i > 0; i--) {
            const testPath = '/' + segments.slice(0, i).join('/')
            const match = groupedMenus.value
                .flatMap(g => g.items)
                .find(menu => normalizePath(menu.path) === normalizePath(testPath))
            if (match) return match.path
        }
        return null
    }

    let activePath = findBestMatch()

    // 默认回退逻辑
    if (!activePath && !hideList.includes(currentPath)) {
        activePath = `/${currentPath.split('/')[1]}`
    }

    // 最终有效性验证
    if (!groupedMenus.value
        .flatMap(g => g.items)
        .some(menu => normalizePath(menu.path) === normalizePath(activePath))) {
        // activePath = groupedMenus.value[0]?.items[0]?.path || ''
            activePath = currentPath === '/index' ? groupedMenus.value[0]?.items[0]?.path : (appStore.menu?.path||groupedMenus.value[0]?.items[0]?.path);
    }

    activeRoutes(activePath)
    return activePath
})

// 路由变化监听
watch(
    () => route.path,
    () => {
        // 强制触发视图更新
        appStore.$patch({})
    }
)

// 菜单选择处理
const handleModuleOpen = (item) => {
    window.open(item.path+'/component/index', "_blank")
}
// 菜单选择处理
const handleModuleSelect = (path,isCheck = false) => {
    const targetRoute = routers.value.find(item => normalizePath(item.path) === normalizePath(path))
    if (!targetRoute) return
    if (isHttp(path)) {
        window.open(path, "_blank")
    } else if (!targetRoute?.children) {

        const routeMenu = childrenMenus.value.find(item => normalizePath(item.path) === normalizePath(path))
        router.push(routeMenu?.query ? {path, query: JSON.parse(routeMenu.query)} : {path})
        // 新增主入口名称更新逻辑
        appStore.setActiveModule(routeMenu.meta.title) // 需要先在store中添加对应方法
    } else {
        activeRoutes(path)
        // 新增主入口更新逻辑
        // const parentModule = groupedMenus.value
        //     .flatMap(g => g.items)
        //     .find(menu => menu.children?.some(child => child.path === path))
        // if (parentModule) {
        //     console.error(parentModule.meta.title)
        //     appStore.setActiveModule(parentModule.meta.title)
        // }

        // //切换导航，默认首页
        if (isCheck) {
            router.push({path:path+'/page'+path+'/index'})
            topNavChange(null, true)
        }
    }

    // 关闭下拉菜单
    const popper = document.querySelector('.modules-popper')
    if (popper) {
        popper.style.display = 'none'
        popper.style.transform = 'scaleY(0)'
    }
}

// 子菜单处理
const childrenMenus = computed(() => {
    let children = []
    routers.value.forEach(router => {
        router.children?.forEach(child => {
            if (!child.parentPath) {
                child.path = router.path === "/"
                    ? `/${child.path}`
                    : `${router.path}/${child.path}`
                child.parentPath = router.path
            }
            children.push(child)
        })
    })
    return constantRoutes.concat(children.map(c => ({
        ...c,
        path: normalizePath(c.path)
    })))
})

// 激活路由处理
const activeRoutes = (key) => {
    try {
        const normalizedKey = normalizePath(key)
        const routes = childrenMenus.value.filter(item =>
            normalizedKey === normalizePath(item.parentPath) ||
            (normalizedKey === "index" && item.path === "")
        )
        if(settingsStore.topNav) {
          permissionStore.setSidebarRouters(routes.length ? routes : [])
          appStore.toggleSideBarHide(!routes.length)
        }

    } catch (error) {
        console.error('路由激活错误:', error)
        appStore.toggleSideBarHide(true)
    }
}

// 初始化
onMounted(() => {
    const defaultPath = groupedMenus.value[0]?.items[0]?.path
    if (defaultPath) handleModuleSelect(activeModule.value?.path || defaultPath)
})


const subMenuRef = ref(null)
// @mouseenter="handleMouseEnter"
// @mouseleave="handleMouseLeave"
// 处理鼠标进入事件
// const handleMouseEnter = () => {
//   if (subMenuRef.value) {
//     subMenuRef.value.visible = true
//   }
// }
//
// // 处理鼠标离开事件
// const handleMouseLeave = () => {
//   // 阻止默认行为
//   if (subMenuRef.value) {
//     // 这里可以添加一些逻辑来决定是否关闭弹出窗口
//     // 例如，检查鼠标是否在弹出窗口内
//     const popper = document.querySelector('.modules-popper')
//     if (popper) {
//       const isMouseInsidePopper = (event) => {
//         return popper.contains(event.relatedTarget)
//       }
//
//       if (!isMouseInsidePopper(event)) {
//         subMenuRef.value.visible = false
//       }
//     }
//   }
// }
// 处理鼠标进入事件
const handleMouseEnter = () => {
    if (subMenuRef.value) {
        subMenuRef.value.visible = true
    }
}

// 处理鼠标离开事件
const handleMouseLeave = () => {
    // 阻止默认行为
    if (subMenuRef.value) {
        subMenuRef.value.visible = false
    }
}

const getImageUrl = (name) => {
    return new URL(`../../assets/navigation_icons/${name}`, import.meta.url).href;

}

const topNavChange = (e, val) => {
  // console.error(val)
  settingsStore.topNav = val!==undefined ? val : !settingsStore.topNav;
  if (!settingsStore.topNav) {
    appStore.toggleSideBarHide(false);
    let mixedRoutes = [];
    let homeRouter = {"redirect" : "noRedirect",
      "path" : "/",
      "component" : "Layout",
      "hidden" : false,

      "meta" : {
        "noCache" : false,
        "icon" : "dashboard",
        "title" : "首页",
        "frame" : false
      },
      "frame" : false,
      "children" : []
    };
    // mixedRoutes.push(homeRouter);
    // console.error(systemCodes)
    permissionStore.defaultRoutes.forEach(item=>{
      // console.error("=====>",item)
      if(item.path==='' || item.hidden ) {
        mixedRoutes.push(item);
      } else if(systemCodes.includes(item.name) && item.children && item.children.length > 0) {

          item.children.map(child=>{
            // console.error( child)
            if(child.path === 'page' && child.children && child.children.length > 0) {
              const fidx = child.children
                  .find(child1=>child1.path === '/' + item.name + '/index')
              //console.error("xxxx", fidx);
              if(fidx) {
                  homeRouter.children.push({
                    ...fidx,
                    // hidden: false,
                    path: normalizePath(item.path) + '/page' +normalizePath(fidx.path)
                  })

              }

            } else {
              mixedRoutes.push( {
                ...child,
                path: normalizePath(item.path) + '/' +normalizePath(child.path)
              });
            }

        })

      }

    })
    // console.error("=====+++++++++>",homeRouter)
    if(homeRouter.children.length > 0 && homeRouter.children.filter(child=>!child.hidden).length > 0) {
      mixedRoutes.unshift(homeRouter);
    }
    permissionStore.setSidebarRouters(mixedRoutes);
  } else {
    appStore.toggleSideBarHide(true);
    const defaultPath = groupedMenus.value[0]?.items[0]?.path
    if (defaultPath) handleModuleSelect(activeModule.value?.path || defaultPath)
  }
}
</script>

<style lang="scss" scoped>
.enhanced-nav {
    --card-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    --theme-rgb: 24, 144, 255;
    --transition-speed: 0.3s;

    :deep(.el-sub-menu__title) {
        background: linear-gradient(145deg, rgba(var(--theme-rgb), 0.1) 0%, #f8f9fa 100%)!important;
        border-radius: 19px!important;
        padding: 7px !important;
        border: 1px solid rgba(var(--theme-rgb), 0.2)!important;
        transition: all var(--transition-speed) ease!important;
        margin-top: 8px!important;
        height: 60%!important;


        &:hover {
            background: linear-gradient(145deg, rgba(var(--theme-rgb), 0.15) 0%, #f8f9fa 100%);
            box-shadow: 0 2px 8px rgba(var(--theme-rgb), 0.1);
        }

        .el-sub-menu__icon-arrow {
            display: none;
        }
    }

    .main-entry {
        display: flex;
        align-items: center;
        gap: 8px;
        font-weight: 500;
        color: var(--theme);
        font-size: 14px;

        .main-icon {
            width: 20px;
            height: 20px;
            margin-right: 6px;
            flex-shrink: 0;
        }
    }
}

.module-container {
    width: auto;
    padding: 16px;
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.96) 0%, #f8f9fa 100%);
    border-radius: 12px;
    box-shadow: var(--card-shadow);
}

.category-columns {
    display: flex;
    gap: 10px;
}

.category-column {
    flex: 1;
    min-width: 220px;

    .category-title {
        font-size: 15px;
        font-weight: 600;
        color: var(--theme);
        margin-bottom: 12px;
        padding-bottom: 8px;
        //border-bottom: 2px solid rgba(var(--theme-rgb), 1); /* 添加横线 */
        border-bottom: 2px solid #eee;
        text-align: center; /* 文字居中 */
    }
}

.module-grid {
    display: grid;
    grid-template-columns: minmax(172px, 1fr); // 设置最小宽度为150px，可以根据需要调整
    gap: 12px;
    max-height: 400px;
    overflow-y: auto;
    padding: 2px;
}

.module-card {
    height: 65px;
    background: white;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    border: 1px solid rgba(0, 0, 0, 0.05);
    position: relative;
    display: flex;

    overflow-x: auto;
    box-sizing: border-box;

    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: var(--theme);
        opacity: 0;
        transition: opacity 0.2s ease;
    }

    &:hover {
        transform: translateY(-3px);
        box-shadow: var(--card-shadow);

        .module-icon {
            color: var(--theme);
        }

        &::before {
            opacity: 0.05;
        }
    }

    &.active-module {
        border: 1px solid rgba(0, 0, 0, 0.05);
        background: rgba(var(--theme-rgb), 0.05);

        &::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: var(--theme);
        }
    }

    .card-content {
        padding: 7px;
        width: 100%;
        display: flex;
        flex-direction: column;
        align-items: baseline;
        justify-content: center;
        position: relative;
        z-index: 1;

        .module-icon {
            font-size: 24px;
            margin-bottom: 6px;
            color: #666;
            transition: color 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .module-title {
            font-size: 13px;
            font-weight: 500;
            color: #333;
            line-height: 1.3;
            text-align: left;
            min-height: 36px;
            display: flex;
            align-items: center;
            justify-content: left;
            padding: 0 4px;
            word-break: keep-all;
        }
    }
}

@media (max-width: 992px) {
    .module-container {
        width: 600px;
    }

    .module-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .category-columns {
        flex-direction: column;
    }
}

@media (max-width: 768px) {
    .module-container {
        width: 90%;
        padding: 12px;
    }

    .module-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .module-card {
        height: 85px;

        .card-content {
            padding: 8px;

            .module-icon {
                font-size: 22px;
            }

            .module-title {
                font-size: 12px;
                min-height: 34px;
            }
        }
    }
}

@media (max-width: 480px) {
    .module-grid {
        grid-template-columns: 1fr;
    }

    .module-card {
        height: 75px;

        .card-content {
            .module-icon {
                font-size: 20px;
            }

            .module-title {
                font-size: 12px;
                min-height: 30px;
            }
        }
    }
}
.card-content {
    padding: 12px;
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;
    z-index: 1;

    .icon-text-container {
        display: flex;
        align-items: center;
        width: 100%;

        .category-text{
            flex: 1;
        }
        .module-icon {
            font-size: 24px;
            margin-right: 1px; // 调整图标和文字之间的间距
            color: #666;
            transition: color 0.2s ease;
        }

        .module-title {
            font-size: 15px;
            font-weight: 500;
            color: #333;
            line-height: 1.3;
            text-align: left;
            min-height: 22px;
            display: flex;
            align-items: center;
            justify-content: left;
            padding: 0 4px;
        }
        .module-des {
            font-size: 11px;
            font-weight: 500;
            color: #888888;
            line-height: 1.3;
            text-align: left;
            min-height: 22px;
            display: flex;
            align-items: center;
            justify-content: left;
            padding: 0 4px;
        }
    }
}
</style>
