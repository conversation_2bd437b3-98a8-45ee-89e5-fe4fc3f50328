<!--
 * @Author: qikai
 * @LastEditors: qikai
 * @Date: 2022-10-17 20:27:04
 * @LastEditTime: 2023-04-22 09:42:16
 * @description: 注意 apiUrl 机构接口请求地址 ：/***-api/org/******
-->
<template>
    <div class="org-class">
        <el-tooltip
            :content="orgContent"
            effect="dark"
            :disabled="orgContent === ''"
            placement="top-start"
        >
            <div>
                <el-cascader
                    ref="org"
                    v-model="orgCode"
                    style="width: 100%"
                    :options="orgList"
                    :props="propsConfig"
                    clearable
                    :disabled="disabled"
                    filterable
                    :show-all-levels="allLevels"
                    :placeholder="placeholderText"
                    @change="handleChange"
                />
            </div>
        </el-tooltip>
    </div>
</template>

<script lang="ts" setup>
/**
 * rate 组织机构
 * @description
 * @author: qikai
 * @time: 2022-10-08 15:52:02
 * @property {String}    apiUrl    接口请求地址
 * @property {String}    defaultOrgCode    默认组织机
 * @property {String}    icon              中文中间拼接字符
 * @property {String | Number | Array | Object | Boolean}    defaultOrgCode    参数介绍
 * @event {Function} handleOrgCode 选中回调
 * @example  例子
 **/

import { ref, onMounted, watch } from "vue"
import { getOrgTreeApi } from "@/api/data"
import {cosh} from "ol/math";

interface Tree {
    label: string
    orgCode: string
    children?: Tree[]
}

const props = defineProps({
    //请求url
    apiUrl: {
        type: String,
        default: "",
    },
    defaultOrgCode: {
        type: String,
        default: "",
    },
    //是否显示全级别
    allLevels: {
        type: Boolean,
        default: true,
    },
    icon: {
        type: String,
        default: "-",
    },
    disabled: {
        type: Boolean,
        default: false,
    },
    //占位文字
    placeholderText: {
        type: String,
        default: "请选择组织机构",
    },
    params: {
        type: Object,
        default: {},
    },
})
const orgList = ref<Tree[]>([])
const emit = defineEmits(["handleOrgCode"])
const orgCode = ref([])
const orgContent = ref("")
const org = ref<HTMLAudioElement>()
const rootOrgCode = ref("")
const propsConfig = {
    checkStrictly: true,
    value: "orgCode",
    label: "orgName",
    expandTrigger: "hover",
    children: "children",
}
const apiOrg = ref(props.apiUrl)
function defaultVal() {
    if (!props.defaultOrgCode) {
        orgCode.value = []
        return
    }
    switch (props.defaultOrgCode.length) {
        case 12:
            switch (rootOrgCode.value.length) {
                case 2:
                    orgCode.value = Array.of(
                        props.defaultOrgCode.slice(0, 2),
                        props.defaultOrgCode.slice(0, 4),
                        props.defaultOrgCode.slice(0, 6),
                        props.defaultOrgCode.slice(0, 10),
                        props.defaultOrgCode.slice(0, 12)
                    )
                    break
                case 4:
                    orgCode.value = Array.of(
                        props.defaultOrgCode.slice(0, 4),
                        props.defaultOrgCode.slice(0, 6),
                        props.defaultOrgCode.slice(0, 10),
                        props.defaultOrgCode.slice(0, 12)
                    )
                    break
                case 6:
                    orgCode.value = Array.of(
                        props.defaultOrgCode.slice(0, 6),
                        props.defaultOrgCode.slice(0, 10),
                        props.defaultOrgCode.slice(0, 12)
                    )

                    break
                case 10:
                    orgCode.value = Array.of(
                        props.defaultOrgCode.slice(0, 10),
                        props.defaultOrgCode.slice(0, 12)
                    )
                    break
                case 12:
                    orgCode.value = Array.of(props.defaultOrgCode.slice(0, 12))
                    break
            }
            break
        case 10:
            switch (rootOrgCode.value.length) {
                case 2:
                    orgCode.value = Array.of(
                        props.defaultOrgCode.slice(0, 2),
                        props.defaultOrgCode.slice(0, 4),
                        props.defaultOrgCode.slice(0, 6),
                        props.defaultOrgCode.slice(0, 10)
                    )
                    break
                case 4:
                    orgCode.value = Array.of(
                        props.defaultOrgCode.slice(0, 4),
                        props.defaultOrgCode.slice(0, 6),
                        props.defaultOrgCode.slice(0, 10)
                    )
                    break
                case 6:
                    orgCode.value = Array.of(
                        props.defaultOrgCode.slice(0, 6),
                        props.defaultOrgCode.slice(0, 10)
                    )
                    break
                case 10:
                    orgCode.value = Array.of(props.defaultOrgCode.slice(0, 10))
                    break
            }
            break
        case 6:
            switch (rootOrgCode.value.length) {
                case 2:
                    orgCode.value = Array.of(
                        props.defaultOrgCode.slice(0, 2),
                        props.defaultOrgCode.slice(0, 4),
                        props.defaultOrgCode.slice(0, 6)
                    )
                    break
                case 4:
                    orgCode.value = Array.of(
                        props.defaultOrgCode.slice(0, 4),
                        props.defaultOrgCode.slice(0, 6)
                    )
                    break
                case 6:
                    orgCode.value = Array.of(props.defaultOrgCode.slice(0, 6))
                    break
            }
            break
        case 4:
            switch (rootOrgCode.value.length) {
                case 2:
                    orgCode.value = Array.of(
                        props.defaultOrgCode.slice(0, 2),
                        props.defaultOrgCode.slice(0, 4)
                    )
                    break
                case 4:
                    orgCode.value = Array.of(props.defaultOrgCode.slice(0, 4))
                    break
            }
            break
        case 2:
            switch (rootOrgCode.value.length) {
                case 2:
                    orgCode.value = Array.of(props.defaultOrgCode.slice(0, 2))
                    break
            }
            break
    }
}
const getOrgTree = () => {


    getOrgTreeApi(apiOrg.value,props.params).then((res) => {
        orgList.value = res.data
        if (orgList.value?.length > 0) {
            localStorage.setItem("OrgTree", JSON.stringify(res.data))
            // rootOrgCode.value = orgList.value[0].orgCode
            // defaultVal()
        }
    })

    let isOrgTree = JSON.parse(localStorage.getItem("OrgTree")) || []
    if (isOrgTree.length === 0) {
        getOrgTreeApi(apiOrg.value,props.params).then((res) => {
            orgList.value = res.data
            if (orgList.value?.length > 0) {
                localStorage.setItem("OrgTree", JSON.stringify(res.data))
                rootOrgCode.value = orgList.value[0].orgCode
                defaultVal()
            }
        })
    } else {
        orgList.value = JSON.parse(JSON.stringify(isOrgTree))
        rootOrgCode.value = isOrgTree[0].orgCode
        defaultVal()
    }
}
const handleChange = (orgList) => {

    let node = org.value.getCheckedNodes()[0]
    if(node){
        let orgName = node.text
        orgContent.value = orgName
        let orgCode = orgList === null ? null : orgList.at(-1)
        org.value.togglePopperVisible()
        emit("handleOrgCode", { orgCode, orgName })
    }else{
        org.value.togglePopperVisible()
        emit("handleOrgCode", { })
    }
    
}
getOrgTree()
onMounted(() => {
})

watch(
    () => props.defaultOrgCode,
    (value, newValue) => {
        nextTick(() => {
            let node = org.value.getCheckedNodes()[0]
            let orgName = node?.text || ""
            orgContent.value = orgName
        })

        defaultVal()
    }
)

//清空组织机构
const clear = () => {
    orgCode.value = null
    orgContent.value = ""
}
defineExpose({
    clear,
})
</script>

<style lang="scss" scoped>
.org-class {
    width: 100% !important;
}
</style>
