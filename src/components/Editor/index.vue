<template>
    <div className="editor"  v-loading="data.loading">
        <el-upload class="avatar-uploader" :action="uploadFileUrl" :show-file-list="false" accept=".png,.jpg,.jpeg"
            :on-success="uploadSuccess" :before-upload="beforeUpload" :headers="headers">
        </el-upload>
        <quill-editor :key="key" v-model:content="content" contentType="html" @textChange="textChange" :options="options"
            :style="styles" ref="myQuillEditor" />
    </div>
</template>

<script setup>
import { ref, reactive, watch } from 'vue';
import { QuillEditor } from '@vueup/vue-quill';
import '@vueup/vue-quill/dist/vue-quill.snow.css';
import { getToken } from "@/utils/auth";
const { proxy } = getCurrentInstance()
const key = ref(1);
const props = defineProps({
    //各项目前缀
    urlApi: {
        type: String,
        default: "",
    },
    modelValue: [String, null], //编辑器的内容
    height: {
        type: Number,
        default: null,
    },
    minHeight: {
        type: Number,
        default: null,
    },
    readOnly: {
        type: Boolean,
        default: false,
    },
});
// 上传文件服务器地址
const uploadFileUrl = computed(
    () => {
        return window.VITE_APP_BASE_API + `/${props.urlApi}/file/upload`;
    }
);
const headers = ref({ 'Access-Token': getToken() });
const myQuillEditor = ref();
const options = reactive({
    theme: 'snow',
    bounds: document.body,
    debug: 'warn',
    modules: {
        toolbar: {
            container: [
                ["bold", "italic", "underline", "strike"],       // 加粗 斜体 下划线 删除线
                ["blockquote"],                                  // 引用  代码块
                [{ list: "ordered" }, { list: "bullet" }],       // 有序、无序列表
                [{ indent: "-1" }, { indent: "+1" }],            // 缩进
                [{ size: ["small", false, "large", "huge"] }],   // 字体大小
                [{ header: [1, 2, 3, 4, 5, 6, false] }],         // 标题
                [{ color: [] }, { background: [] }],             // 字体颜色、字体背景颜色
                [{ align: [] }],                                 // 对齐方式
                ["clean"],                                       // 清除文本格式
                // ["link", "image"]                                // 链接、图片、视频
                ["image"]                                // 链接、图片、视频
            ],
            handlers: {
                image: function (value) {
                    if (value) {
                        document.querySelector(".avatar-uploader input").click();
                    } else {
                        let quill = myQuillEditor.value.getQuill();
                        quill.format("image", false);
                    }
                },
            },
        },
    },
});
function uploadSuccess(res, file) {
    data.loading=false
    // 获取富文本组件实例
    let quill = myQuillEditor.value.getQuill();
    // 如果上传成功
    if (res.code === 0) {
        // 获取光标所在位置
        let length = quill.getSelection().index;
        // 插入图片，res为服务器返回的图片链接地址
        quill.insertEmbed(length, "image", res.data);
        proxy.$nextTick(()=>{
            setTimeout(() => {
                quill.setSelection(length + 1);
            })
        })

    } else {
        proxy.$modal.msgError('图片插入失败！')
    }

}
function textChange(e) {
    proxy.$emit('update:modelValue', content)
}
const data = reactive({
    fileType: ['png', 'jpg', 'jpeg'],
    fileSize: 5,
    loading:false
});
function beforeUpload(file) {
    // 校检文件类型
    if (data.fileType.length) {
        const fileName = file.name.split('.');
        const fileExt = fileName[fileName.length - 1];
        const isTypeOk = data.fileType.indexOf(fileExt) >= 0;
        if (!isTypeOk) {
            proxy.$modal.msgError(`文件格式不正确, 请上传${data.fileType.join("/")}格式文件!`);
            return false;
        }
    }
    // 校检文件大小
    if (data.fileSize) {
        const isLt = file.size / 1024 / 1024 < data.fileSize;
        if (!isLt) {
            proxy.$modal.msgError(`上传文件大小不能超过 ${data.fileSize} MB!`);
            return false;
        }
    }
    data.loading=true
}
const styles = reactive({
    minHeight: null,
    height: null,
});

const content = ref('');
const showEditor = ref(true);

watch(() => props.readOnly, (newValue) => {
    options.readOnly = props.readOnly;
    key.value++

}, { immediate: true });

watch(() => props.modelValue, (newValue) => {
    if (newValue !== content.value) {
        content.value = newValue || '<p></p>';
    }
}, { immediate: true });

if (props.minHeight) {
    styles.minHeight = `${props.minHeight}px`;
}

if (props.height) {
    styles.height = `${props.height}px`;
}
</script>

<style>
/* 样式省略 */
</style>
